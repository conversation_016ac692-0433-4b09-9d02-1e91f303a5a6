package kr.wayplus.qr_hallimpark.config;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import kr.wayplus.qr_hallimpark.service.PageService;

/**
 * 사용자 정보 기록을 위해 쿠키 기반으로 접속자 고유 아이디를 확인한다.
 */
@Component
public class UserPageCommonInterceptor implements HandlerInterceptor {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final PageService pageService;

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookiePrefix;

    @Autowired
    public UserPageCommonInterceptor(PageService pageService) {
        this.pageService = pageService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        logger.debug("================== Start User Page Common Interceptor ==================");

        // 현재 URL 정보를 request attribute에 추가
        String currentUrl = getCurrentUrl(request);
        request.setAttribute("currentUrl", currentUrl);

        return true;
    }

    /**
     * 현재 요청 URL을 가져오는 메서드
     */
    private String getCurrentUrl(HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();
        String requestURI = request.getRequestURI();
        String queryString = request.getQueryString();

        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);

        // 기본 포트가 아닌 경우에만 포트 추가
        if ((scheme.equals("http") && serverPort != 80) ||
            (scheme.equals("https") && serverPort != 443)) {
            url.append(":").append(serverPort);
        }

        url.append(requestURI);

        if (queryString != null) {
            url.append("?").append(queryString);
        }

        return url.toString();
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                    @Nullable ModelAndView modelAndView) throws Exception {
        try {
            logger.debug("Call User Page Common Interceptor...");

            // ModelAndView가 있고 뷰 이름이 있는 경우에만 처리
            if (modelAndView != null && modelAndView.getViewName() != null &&
                !modelAndView.getViewName().startsWith("redirect:")) {

                // 현재 URL을 모델에 추가
                String currentUrl = getCurrentUrl(request);
                modelAndView.addObject("currentUrl", currentUrl);

                logger.debug("Added currentUrl to model: {}", currentUrl);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        logger.debug("================== End User Page Common Interceptor ==================");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                 @Nullable Exception ex) throws Exception {
    }


}
