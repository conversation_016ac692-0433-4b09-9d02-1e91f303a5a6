/* 한림공원 QR 체험 - 관리자 인증 페이지 스타일 */

/* 관리자 로그인 컨테이너 */
.admin-login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
}

/* 관리자 로그인 카드 */
.admin-login-card {
    max-width: 450px;
    margin: 0 auto;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    overflow: hidden;
}

/* 관리자 로그인 헤더 */
.admin-login-header {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    color: white;
    border-radius: 20px 20px 0 0;
    padding: 2.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.admin-login-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 관리자 로그인 본문 */
.admin-login-body {
    padding: 2.5rem;
}

/* 관리자 아이콘 */
.admin-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: white;
}

/* 관리자 폼 그룹 */
.admin-form-group {
    margin-bottom: 1.5rem;
}

/* 관리자 폼 라벨 */
.admin-form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

/* 관리자 폼 컨트롤 */
.admin-form-control {
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.admin-form-control:focus {
    border-color: #2E8B57;
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

/* 관리자 로그인 버튼 */
.admin-login-btn {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    border: none;
    border-radius: 10px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    width: 100%;
}

.admin-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(46, 139, 87, 0.3);
}

/* 관리자 경고 메시지 */
.admin-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    color: #856404;
    text-align: center;
}

/* 관리자 보안 정보 */
.admin-security-info {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
}

.admin-security-info .security-icon {
    font-size: 1.5rem;
    color: #28a745;
    margin-bottom: 0.5rem;
}

/* 반응형 디자인 */
@media (max-width: 576px) {
    .admin-login-card {
        margin: 1rem;
        max-width: none;
    }
    
    .admin-login-header {
        padding: 2rem 1.5rem;
    }
    
    .admin-login-body {
        padding: 2rem 1.5rem;
    }
    
    .admin-icon {
        font-size: 3rem;
    }
}

/* 폼 플로팅 스타일 개선 */
.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    transition: all 0.3s ease;
}

.form-floating .form-control:focus {
    border-color: #2a5298;
    box-shadow: 0 0 0 0.2rem rgba(42, 82, 152, 0.25);
}

/* 관리자 로그인 버튼 개선 */
.btn-admin-login {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    border: none;
    border-radius: 12px;
    padding: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-admin-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(42, 82, 152, 0.4);
}

.btn-admin-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-admin-login:hover::before {
    left: 100%;
}

/* 기억하기 체크박스 개선 */
.remember-me {
    margin: 1.5rem 0;
}

/* 관리자 로그인 푸터 개선 */
.admin-login-footer {
    text-align: center;
    padding: 1.5rem 2.5rem 2.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 20px 20px;
}

/* 관리자 배지 */
.admin-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

/* 보안 안내 */
.security-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}
