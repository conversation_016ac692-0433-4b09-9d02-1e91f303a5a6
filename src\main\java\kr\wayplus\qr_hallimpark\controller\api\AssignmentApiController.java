package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.model.AssignmentCreateRequest;
import kr.wayplus.qr_hallimpark.model.AssignmentCreateResponse;
import kr.wayplus.qr_hallimpark.model.AssignmentDeleteRequest;
import kr.wayplus.qr_hallimpark.model.AssignmentDeleteResponse;
import kr.wayplus.qr_hallimpark.model.QrQuizBatchRequest;
import kr.wayplus.qr_hallimpark.model.QrQuizBatchResponse;
import kr.wayplus.qr_hallimpark.model.QrQuizMapping;
import kr.wayplus.qr_hallimpark.service.QrQuizMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Assignment API 컨트롤러
 * - API 서버에서 호출하는 QR-문제 연결 관련 API
 * - 서버 간 통신: API 키 인증 필요 (WayQRConnect)
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class AssignmentApiController {

    private final QrQuizMappingService qrQuizMappingService;

    /**
     * QR-문제 연결 생성 (API 서버용)
     * @param request 연결 생성 요청
     * @return 연결 생성 결과
     */
    @PostMapping("/assignments")
    public HashMap<String, Object> createAssignment(@RequestBody AssignmentCreateRequest request) {
        log.debug("Creating QR-Quiz assignment: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateCreateRequest(request);

            // QrQuizMapping 생성 (생성자는 시스템으로 설정)
            QrQuizMapping mapping = request.toQrQuizMapping("SYSTEM_API");

            // 연결 생성
            QrQuizMapping createdMapping = qrQuizMappingService.createMapping(mapping);

            // 생성된 매핑 정보 조회 (조인 정보 포함)
            QrQuizMapping fullMapping = qrQuizMappingService.findMappingById(createdMapping.getMappingId());

            // 응답 데이터 생성
            AssignmentCreateResponse assignmentResponse = AssignmentCreateResponse.fromQrQuizMapping(fullMapping);

            response.put("success", true);
            response.put("message", "연결이 성공적으로 생성되었습니다.");
            response.put("data", assignmentResponse);

            log.info("QR-Quiz assignment created successfully. MappingId: {}, qrCodeId: {}, QuizId: {}", 
                    createdMapping.getMappingId(), request.getQrCodeId(), request.getQuizId());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid assignment request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error creating QR-Quiz assignment: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 생성 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR ID로 연결된 문제 목록 조회 (API 서버용)
     * @param qrCodeId QR 코드 ID
     * @return 연결된 문제 목록
     */
    @GetMapping("/assignments/qr/{qrCodeId}")
    public HashMap<String, Object> getAssignmentsByQrCodeId(@PathVariable("qrCodeId") String qrCodeId) {
        log.debug("Getting assignments by QR ID: {}", qrCodeId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QrQuizMapping> mappings = qrQuizMappingService.findMappingsByQrCodeId(qrCodeId);

            List<AssignmentCreateResponse> assignments = mappings.stream()
                    .map(AssignmentCreateResponse::fromQrQuizMapping)
                    .collect(Collectors.toList());

            response.put("success", true);
            response.put("data", assignments);

            log.debug("Successfully retrieved {} assignments for QR ID: {}", assignments.size(), qrCodeId);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid QR ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_QR_CODE_ID");

        } catch (Exception e) {
            log.error("Error retrieving assignments for QR ID {}: {}", qrCodeId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 정보 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 여러 QR 코드의 연결된 문제 목록 조회 (외부 API용)
     * @param request QR 코드 ID 목록 요청
     * @return QR 코드별 연결된 문제 목록
     */
    @PostMapping("/assignments/batch")
    public HashMap<String, Object> getAssignmentsBatch(@RequestBody QrQuizBatchRequest request) {
        log.debug("Getting assignments batch: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateBatchRequest(request);

            // 여러 QR ID로 매핑 목록 조회
            List<QrQuizMapping> allMappings = qrQuizMappingService.findMappingsByQrCodeIds(
                    request.getQrCodeIds(),
                    request.getActiveOnly()
            );

            // QR ID별로 그룹화
            Map<String, List<QrQuizMapping>> groupedMappings = allMappings.stream()
                    .collect(Collectors.groupingBy(QrQuizMapping::getQrCodeId));

            // 응답 데이터 생성
            List<QrQuizBatchResponse> batchResponses = request.getQrCodeIds().stream()
                    .map(qrCodeId -> {
                        List<QrQuizMapping> mappings = groupedMappings.getOrDefault(qrCodeId, List.of());
                        List<QrQuizBatchResponse.QrQuizInfo> quizInfos = mappings.stream()
                                .map(QrQuizBatchResponse.QrQuizInfo::fromQrQuizMapping)
                                .collect(Collectors.toList());

                        return QrQuizBatchResponse.builder()
                                .qrCodeId(qrCodeId)
                                .quizzes(quizInfos)
                                .quizCount(quizInfos.size())
                                .build();
                    })
                    .collect(Collectors.toList());

            response.put("success", true);
            response.put("data", batchResponses);
            response.put("totalQrCount", request.getQrCodeIds().size());
            response.put("totalQuizCount", allMappings.size());

            log.debug("Successfully retrieved batch assignments for {} QR codes, {} total quizzes",
                    request.getQrCodeIds().size(), allMappings.size());

        } catch (IllegalArgumentException e) {
            log.warn("Invalid batch request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error retrieving batch assignments: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "배치 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * QR-문제 연결 해제 (API 서버용)
     * - QR ID와 문제 ID로 특정 연결 해제
     * - 매핑 ID로 직접 해제도 지원
     * @param request 연결 해제 요청
     * @return 연결 해제 결과
     */
    @PostMapping("/assignments/unlink")
    public HashMap<String, Object> unlinkAssignment(@RequestBody AssignmentDeleteRequest request) {
        log.debug("Unlinking QR-Quiz assignment: {}", request);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 요청 데이터 유효성 검증
            validateDeleteRequest(request);

            QrQuizMapping deletedMapping;
            AssignmentDeleteResponse deleteResponse;
        
            // QR ID와 문제 ID로 삭제
            deletedMapping = qrQuizMappingService.deleteMappingByQrCodeIdAndQuizId(
                    request.getQrCodeId(), request.getQuizId(), "SYSTEM_API");

            deleteResponse = AssignmentDeleteResponse.fromQrQuizMapping(deletedMapping, request.getDeleteReason());

            log.info("QR-Quiz assignment unlinked by qrCodeId and QuizId. qrCodeId: {}, QuizId: {}, MappingId: {}",
                    request.getQrCodeId(), request.getQuizId(), deletedMapping.getMappingId());
            

            response.put("success", true);
            response.put("message", "연결이 성공적으로 해제되었습니다.");
            response.put("data", deleteResponse);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid unlink request: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_REQUEST");

        } catch (Exception e) {
            log.error("Error unlinking QR-Quiz assignment: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "연결 해제 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * CORS Preflight 요청 처리 (OPTIONS 메서드)
     * @return 성공 응답
     */
    @RequestMapping(value = "/assignments/**", method = RequestMethod.OPTIONS)
    public HashMap<String, Object> handleOptions() {
        HashMap<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "CORS preflight request handled successfully");
        return response;
    }

    /**
     * 연결 생성 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validateCreateRequest(AssignmentCreateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (request.getQrCodeId() == null || request.getQrCodeId().trim().isEmpty()) {
            throw new IllegalArgumentException("QR ID는 필수입니다.");
        }

        if (request.getQuizId() == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }
    }

    /**
     * 배치 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validateBatchRequest(QrQuizBatchRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (request.getQrCodeIds() == null || request.getQrCodeIds().isEmpty()) {
            throw new IllegalArgumentException("QR ID 목록은 필수입니다.");
        }

        if (request.getQrCodeIds().size() > 100) {
            throw new IllegalArgumentException("한 번에 조회할 수 있는 QR 코드는 최대 100개입니다.");
        }

        // 빈 문자열이나 null 값 체크
        long validQrCodeIdCount = request.getQrCodeIds().stream()
                .filter(qrCodeId -> qrCodeId != null && !qrCodeId.trim().isEmpty())
                .count();

        if (validQrCodeIdCount == 0) {
            throw new IllegalArgumentException("유효한 QR ID가 없습니다.");
        }
    }

    /**
     * 연결 해제 요청 유효성 검증
     * @param request 요청 데이터
     */
    private void validateDeleteRequest(AssignmentDeleteRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("요청 데이터는 필수입니다.");
        }

        if (!request.isValid()) {
            throw new IllegalArgumentException("매핑 ID 또는 QR ID와 문제 ID가 필요합니다.");
        }

        // 매핑 ID로 삭제하는 경우
        if (request.isDirectMappingDelete()) {
            if (request.getMappingId() <= 0) {
                throw new IllegalArgumentException("유효하지 않은 매핑 ID입니다.");
            }
        } else {
            // QR ID와 문제 ID로 삭제하는 경우
            if (request.getQrCodeId().trim().length() > 100) {
                throw new IllegalArgumentException("QR ID가 너무 깁니다. (최대 100자)");
            }

            if (request.getQuizId() <= 0) {
                throw new IllegalArgumentException("유효하지 않은 문제 ID입니다.");
            }
        }
    }
}
