package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Quiz API 응답 모델
 * - Vue3에서 사용할 문제 정보 (외부 노출용)
 * - 최소한의 필요한 정보만 포함
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuizApiResponse {

    /**
     * 문제 고유 ID
     */
    private Long quizId;

    /**
     * 문제 제목 (관리용)
     */
    private String title;

    /**
     * 문제 질문 내용 (한국어)
     */
    private String question;

    /**
     * 카테고리명
     */
    private String categoryName;

    /**
     * 부모 카테고리명 (서브카테고리인 경우)
     */
    private String parentCategoryName;

    /**
     * 카테고리 전체 경로 (부모 > 자식)
     */
    private String categoryPath;

    /**
     * 문제 유형
     * - MCQ: 객관식
     * - OX: OX 퀴즈
     * - ORDER: 순서 정렬
     * - IMAGE_VOICE: 이미지/음성 인식
     * - PUZZLE_MEMORY: 퍼즐/기억력 게임
     */
    private String quizType;

    /**
     * 문제 상태
     * - ACTIVE: 활성
     * - INACTIVE: 비활성
     */
    private String status;

    /**
     * 난이도 (1~5)
     */
    private Integer difficultyLevel;

    /**
     * Quiz 모델에서 QuizApiResponse로 변환
     * @param quiz Quiz 모델
     * @return QuizApiResponse
     */
    public static QuizApiResponse fromQuiz(Quiz quiz) {
        return QuizApiResponse.builder()
                .quizId(quiz.getQuizId())
                .title(quiz.getTitle())
                .question(quiz.getQuestion())
                .categoryName(quiz.getCategoryName())
                .parentCategoryName(quiz.getParentCategoryName())
                .categoryPath(quiz.getCategoryPath())
                .quizType(quiz.getQuizType())
                .status(quiz.getStatus())
                .difficultyLevel(quiz.getDifficultyLevel())
                .build();
    }
}
