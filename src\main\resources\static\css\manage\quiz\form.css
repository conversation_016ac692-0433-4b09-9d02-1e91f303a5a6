/* 문제 등록/수정 폼 스타일 */

/* 메인 컨테이너 */
.quiz-form-main {
    margin-left: 250px;
    padding: 20px;
    min-height: calc(100vh - 120px);
    background-color: #f8f9fa;
}

.quiz-form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 페이지 헤더 */
.quiz-form-header {
    background: #007bff;
    color: white;
    padding: 30px;
    text-align: center;
}

.quiz-form-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.quiz-form-header p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

/* 폼 스타일 */
.quiz-form {
    padding: 30px;
}

.quiz-form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.quiz-form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 20px;
}

.quiz-form-section h2 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
}

/* 폼 필드 */
.quiz-form-field {
    margin-bottom: 20px;
}

.quiz-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.quiz-form-sublabel {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #666;
    font-size: 13px;
}

.required {
    color: #dc3545;
}

/* 입력 요소 */
.quiz-form-input,
.quiz-form-select,
.quiz-form-textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.quiz-form-input:focus,
.quiz-form-select:focus,
.quiz-form-textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.quiz-form-textarea {
    min-height: 100px;
    resize: vertical;
}

/* 카테고리 컨테이너 */
.quiz-form-category-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.quiz-form-category-field {
    display: flex;
    flex-direction: column;
}

/* 언어 선택 */
.quiz-form-language-selector {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.quiz-form-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: normal;
}

.quiz-form-checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* 언어 선택 도움말 */
.quiz-form-help-text {
    font-size: 14px;
    color: #666;
    margin-top: 8px;
    line-height: 1.4;
}

/* QR 연동 버튼 */
.quiz-form-qr-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.quiz-form-qr-btn:hover {
    background: #218838;
}

.quiz-form-help-text {
    margin: 10px 0 0 0;
    font-size: 13px;
    color: #666;
}

/* 버튼 영역 */
.quiz-form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.quiz-form-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.quiz-form-btn-primary {
    background: #007bff;
    color: white;
}

.quiz-form-btn-primary:hover {
    background: #0056b3;
}

.quiz-form-btn-secondary {
    background: #6c757d;
    color: white;
}

.quiz-form-btn-secondary:hover {
    background: #545b62;
}

.quiz-form-btn-danger {
    background: #dc3545;
    color: white;
}

.quiz-form-btn-danger:hover {
    background: #c82333;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .quiz-form-main {
        margin-left: 0;
        padding: 10px;
    }
    
    .quiz-form-container {
        margin: 0;
        border-radius: 0;
    }
    
    .quiz-form {
        padding: 20px;
    }
    
    .quiz-form-category-container {
        grid-template-columns: 1fr;
    }
    
    .quiz-form-language-selector {
        flex-direction: column;
        gap: 10px;
    }
    
    .quiz-form-actions {
        flex-direction: column;
    }
    
    .quiz-form-btn {
        width: 100%;
    }
}

/* 로딩 상태 */
.quiz-form-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 에러 상태 */
.quiz-form-error {
    border-color: #dc3545 !important;
}

.quiz-form-error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
}

/* 성공 상태 */
.quiz-form-success {
    border-color: #28a745 !important;
}
