<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage-auth}">
<head>
    <title>관리자 로그인 - 한림공원 QR 체험 관리시스템</title>
    <meta name="description" content="한림공원 QR 체험 관리자 시스템에 로그인하세요.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/auth.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <div class="admin-login-container">
            <div class="admin-login-card">
                <!-- 관리자 로그인 헤더 -->
                <div class="admin-login-header">
                    <p class="opacity-75">한림공원 QR관리자 페이지</p>
                </div>
                
                <!-- 관리자 로그인 폼 -->
                <div class="admin-login-body">
                    <div id="debug-info" style="display: none;">
                        <span id="showLoginError" th:text="${showLoginError}">false</span>
                        <span id="adminLoginError" th:text="${adminLoginError}">false</span>
                        <span id="adminLoginErrorCode" th:text="${adminLoginErrorCode}"></span>
                        <span id="adminLoginErrorMessage" th:text="${adminLoginErrorMessage}"></span>
                        <span id="showLogoutMessage" th:text="${showLogoutMessage}">false</span>
                    </div>
                    
                    <form action="/manage/login-security" method="post" id="adminLoginForm">
                        <!-- 관리자 이메일 입력 -->
                        <div class="form-floating">
                            <input type="email"
                                    class="form-control"
                                    id="admin-email"
                                    name="admin-email"
                                    placeholder="관리자 이메일"
                                    th:value="${savedAdminId}">
                            <label for="admin-email">
                                <i class="fas fa-user-shield"></i>관리자 이메일
                            </label>
                            <div class="invalid-feedback">
                                관리자 이메일 주소를 입력해주세요.
                            </div>
                        </div>
                        
                        <!-- 비밀번호 입력 -->
                        <div class="form-floating">
                            <input type="password"
                                    class="form-control"
                                    id="admin-pass"
                                    name="admin-pass"
                                    placeholder="비밀번호">
                            <label for="admin-pass">
                                <i class="fas fa-lock"></i>비밀번호
                            </label>
                            <div class="invalid-feedback">
                                비밀번호를 입력해주세요.
                            </div>
                        </div>
                        
                        <!-- 아이디 저장 체크박스 -->
                        <div class="form-check remember-me">
                            <input class="form-check-input"
                                    type="checkbox"
                                    id="remember"
                                    name="remember"
                                    value="Y"
                                    th:checked="${rememberChecked}">
                            <label class="form-check-label" for="remember">
                                <i class="fas fa-save me-1"></i>관리자 아이디 저장
                            </label>
                        </div>
                        
                        <!-- 로그인 버튼 -->
                        <button type="submit" class="btn btn-primary btn-admin-login">
                            <i class="fas fa-sign-in-alt"></i>관리자 로그인
                        </button>
                    </form>
                </div>
                
                <!-- 관리자 로그인 푸터 -->
                <div class="admin-login-footer">
                    <div>
                        <a th:href="@{/}" class="text-decoration-none">
                            <i class="fas fa-home"></i>메인 사이트
                        </a>
                        <a th:href="@{/user/login}" class="text-decoration-none">
                            <i class="fas fa-user"></i>일반 로그인
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 추가 스크립트 -->
    <div layout:fragment="scripts">
        <script>
            // 폼 유효성 검사
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    console.log('Page loaded, initializing form validation');
                    var forms = document.getElementsByClassName('needs-validation');
                    console.log('Found forms:', forms.length);

                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            console.log('Form validation triggered');
                            if (form.checkValidity() === false) {
                                console.log('Form validation failed');
                                event.preventDefault();
                                event.stopPropagation();
                            } else {
                                console.log('Form validation passed');
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();
            
            // 로그인 버튼 로딩 처리
            const loginForm = document.querySelector('form');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    console.log('Form submit event triggered');
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn && this.checkValidity()) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>로그인 중...';
                        submitBtn.disabled = true;
                    }
                });
            }
            
            // 이메일 입력 시 자동 포커스 및 에러 메시지 처리
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM Content Loaded');

                // 서버에서 전달된 속성 확인 (숨겨진 요소에서 읽기)
                console.log('Checking for messages...');

                // 숨겨진 요소에서 값 읽기
                const showLoginErrorElement = document.getElementById('showLoginError');
                const adminLoginErrorElement = document.getElementById('adminLoginError');
                const adminLoginErrorCodeElement = document.getElementById('adminLoginErrorCode');
                const adminLoginErrorMessageElement = document.getElementById('adminLoginErrorMessage');
                const showLogoutMessageElement = document.getElementById('showLogoutMessage');

                const showLoginError = showLoginErrorElement ? showLoginErrorElement.textContent === 'true' : false;
                const adminLoginError = adminLoginErrorElement ? adminLoginErrorElement.textContent === 'true' : false;
                const adminLoginErrorCode = adminLoginErrorCodeElement ? adminLoginErrorCodeElement.textContent : '';
                const adminLoginErrorMessage = adminLoginErrorMessageElement ? adminLoginErrorMessageElement.textContent : '';
                const showLogoutMessage = showLogoutMessageElement ? showLogoutMessageElement.textContent === 'true' : false;

                console.log('Debug info from hidden elements:');
                console.log('- showLoginError:', showLoginError);
                console.log('- adminLoginError:', adminLoginError);
                console.log('- adminLoginErrorCode:', adminLoginErrorCode);
                console.log('- adminLoginErrorMessage:', adminLoginErrorMessage);
                console.log('- showLogoutMessage:', showLogoutMessage);

                // 로그인 에러 메시지 표시
                if ((showLoginError || adminLoginError) && adminLoginErrorMessage && adminLoginErrorMessage.trim() !== '') {
                    console.log('Showing error alert:', adminLoginErrorMessage);
                    alert(adminLoginErrorMessage);
                } else if (showLoginError || adminLoginError) {
                    console.log('Error flag is true but no message');
                }

                const emailInput = document.getElementById('admin-email');
                if (!emailInput.value) {
                    emailInput.focus();
                } else {
                    document.getElementById('admin-pass').focus();
                }
            });
        </script>
    </div>
</body>
</html>
