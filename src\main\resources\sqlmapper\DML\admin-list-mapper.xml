<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper">

    <!-- 공통 동적 검색 조건 SQL Fragment -->
    <sql id="commonSearchConditions">
        <where>
            <!-- 기본 조건 (delete_yn = 'N' 등) -->
            <if test="baseCondition != null and baseCondition != ''">
                ${baseCondition}
            </if>
            
            <!-- 검색 키워드 조건 -->
            <if test="searchKeyword != null and searchKeyword != '' and searchFields != null and !searchFields.isEmpty()">
                AND (
                <foreach collection="searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 필터 조건들 -->
            <if test="filters != null and !filters.isEmpty()">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>

            <!-- 날짜 범위 조건 -->
            <if test="dateFrom != null and dateTo != null and dateField != null and dateField != ''">
                AND ${dateField} BETWEEN #{dateFrom} AND #{dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="numberFrom != null and numberTo != null and numberField != null and numberField != ''">
                AND ${numberField} BETWEEN #{numberFrom} AND #{numberTo}
            </if>
        </where>
    </sql>
    
    <!-- 공통 정렬 조건 SQL Fragment -->
    <sql id="commonOrderBy">
        <if test="sortField != null and sortField != '' and sortDirection != null and (sortDirection == 'ASC' or sortDirection == 'DESC')">
            ORDER BY ${sortField} ${sortDirection}
        </if>
        <if test="sortField == null or sortField == '' or sortDirection == null or (sortDirection != 'ASC' and sortDirection != 'DESC')">
            ORDER BY create_date DESC
        </if>
    </sql>
    
    <!-- 공통 페이징 조건 SQL Fragment -->
    <sql id="commonPaging">
        LIMIT #{offset}, #{limit}
    </sql>
    
    <!-- 동적 리스트 조회 템플릿 -->
    <select id="selectListWithConditions" parameterType="AdminListSearch" resultType="map">
        SELECT *
        FROM ${tableName}
        <include refid="commonSearchConditions"/>
        <include refid="commonOrderBy"/>
        <include refid="commonPaging"/>
    </select>
    
    <!-- 동적 개수 조회 템플릿 -->
    <select id="countWithConditions" parameterType="AdminListSearch" resultType="long">
        SELECT COUNT(*)
        FROM ${tableName}
        <include refid="commonSearchConditions"/>
    </select>
    
    <!-- 특정 테이블용 동적 리스트 조회 (테이블명을 파라미터로 받지 않는 버전) -->
    <select id="selectListWithConditionsForTable" parameterType="map" resultType="map">
        SELECT *
        FROM ${tableName}
        <where>
            <!-- 기본 조건 -->
            <if test="searchCondition.baseCondition != null and searchCondition.baseCondition != ''">
                ${searchCondition.baseCondition}
            </if>
            
            <!-- 검색 키워드 조건 -->
            <if test="searchCondition.searchKeyword != null and searchCondition.searchKeyword != '' and searchCondition.searchFields != null and !searchCondition.searchFields.isEmpty()">
                AND (
                <foreach collection="searchCondition.searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchCondition.searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 필터 조건들 -->
            <if test="searchCondition.filters != null and !searchCondition.filters.isEmpty()">
                <foreach collection="searchCondition.filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>

            <!-- 날짜 범위 조건 -->
            <if test="searchCondition.dateFrom != null and searchCondition.dateTo != null and searchCondition.dateField != null and searchCondition.dateField != ''">
                AND ${searchCondition.dateField} BETWEEN #{searchCondition.dateFrom} AND #{searchCondition.dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="searchCondition.numberFrom != null and searchCondition.numberTo != null and searchCondition.numberField != null and searchCondition.numberField != ''">
                AND ${searchCondition.numberField} BETWEEN #{searchCondition.numberFrom} AND #{searchCondition.numberTo}
            </if>
        </where>
        <if test="searchCondition.sortField != null and searchCondition.sortField != '' and searchCondition.sortDirection != null and (searchCondition.sortDirection == 'ASC' or searchCondition.sortDirection == 'DESC')">
            ORDER BY ${searchCondition.sortField} ${searchCondition.sortDirection}
        </if>
        <if test="searchCondition.sortField == null or searchCondition.sortField == '' or searchCondition.sortDirection == null or (searchCondition.sortDirection != 'ASC' and searchCondition.sortDirection != 'DESC')">
            ORDER BY create_date DESC
        </if>
        LIMIT #{searchCondition.offset}, #{searchCondition.limit}
    </select>
    
    <!-- 특정 테이블용 동적 개수 조회 -->
    <select id="countWithConditionsForTable" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM ${tableName}
        <where>
            <!-- 기본 조건 -->
            <if test="searchCondition.baseCondition != null and searchCondition.baseCondition != ''">
                ${searchCondition.baseCondition}
            </if>
            
            <!-- 검색 키워드 조건 -->
            <if test="searchCondition.searchKeyword != null and searchCondition.searchKeyword != '' and searchCondition.searchFields != null and !searchCondition.searchFields.isEmpty()">
                AND (
                <foreach collection="searchCondition.searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchCondition.searchKeyword}, '%')
                </foreach>
                )
            </if>

            <!-- 필터 조건들 -->
            <if test="searchCondition.filters != null and !searchCondition.filters.isEmpty()">
                <foreach collection="searchCondition.filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>

            <!-- 날짜 범위 조건 -->
            <if test="searchCondition.dateFrom != null and searchCondition.dateTo != null and searchCondition.dateField != null and searchCondition.dateField != ''">
                AND ${searchCondition.dateField} BETWEEN #{searchCondition.dateFrom} AND #{searchCondition.dateTo}
            </if>

            <!-- 숫자 범위 조건 -->
            <if test="searchCondition.numberFrom != null and searchCondition.numberTo != null and searchCondition.numberField != null and searchCondition.numberField != ''">
                AND ${searchCondition.numberField} BETWEEN #{searchCondition.numberFrom} AND #{searchCondition.numberTo}
            </if>
        </where>
    </select>

</mapper>
