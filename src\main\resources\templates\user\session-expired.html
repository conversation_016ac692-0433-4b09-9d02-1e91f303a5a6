<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/user}">
<head>
    <title>세션 만료 - 한림공원 QR 체험</title>
    <meta name="description" content="세션이 만료되었습니다.">
</head>
<body>
    <div layout:fragment="content">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 col-md-10">
                    <div>
                        <!-- 세션 만료 아이콘 -->
                        <div>
                            <i class="fas fa-clock text-warning" style="font-size: 6rem;"></i>
                        </div>

                        <!-- 메시지 -->
                        <h1 class="h2">세션이 만료되었습니다</h1>
                        <p class="text-muted">
                            보안을 위해 일정 시간 후 자동으로 로그아웃됩니다.<br>
                            계속 이용하시려면 다시 로그인해주세요.
                        </p>

                        <!-- 액션 버튼들 -->
                        <div class="action-buttons">
                            <a th:href="@{/user/login}" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i>다시 로그인하기
                            </a>
                            <a th:href="@{/}" class="btn btn-outline-primary">
                                <i class="fas fa-home"></i>홈으로 돌아가기
                            </a>
                        </div>
                        
                        <!-- 게스트 이용 안내 -->
                        <div class="alert alert-info" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle"></i>로그인 없이도 이용 가능합니다
                            </h6>
                            <p>다음 서비스들은 로그인 없이도 이용하실 수 있습니다:</p>
                            <div class="service-buttons">
                                <a th:href="@{/quiz}" class="btn btn-outline-info">
                                    <i class="fas fa-question-circle"></i>퀴즈 체험
                                </a>
                                <a th:href="@{/story}" class="btn btn-outline-info">
                                    <i class="fas fa-book"></i>스토리 모드
                                </a>
                                <a th:href="@{/map}" class="btn btn-outline-info">
                                    <i class="fas fa-map"></i>지도 보기
                                </a>
                            </div>
                        </div>
                        
                        <!-- 세션 관련 안내 -->
                        <div class="session-info">
                            <h6>세션 관리 안내</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-card">
                                        <h6>
                                            <i class="fas fa-shield-alt"></i>보안 정책
                                        </h6>
                                        <p>
                                            사용자 정보 보호를 위해 일정 시간 비활성 상태가 지속되면
                                            자동으로 로그아웃됩니다.
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-card">
                                        <h6>
                                            <i class="fas fa-tips"></i>이용 팁
                                        </h6>
                                        <p>
                                            로그인 상태를 유지하려면 주기적으로
                                            페이지를 이용해주세요.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
