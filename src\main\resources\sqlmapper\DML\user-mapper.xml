<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.qr_hallimpark.mapper.UserMapper">
    <select id="selectUserByUserid" parameterType="String" resultType="LoginUser">
        SELECT user_email, user_pass, user_name, user_birthday,
               user_tel, user_mobile, user_role, user_gender,
               user_group_code, user_token_id,
               mailing_yn,
               user_join_date, last_login_date, last_password_date,
               naver_token, kakao_token, google_token, secondary_email
          FROM user
         WHERE user_email = #{value}
    </select>

    <update id="updateUserSessionLogout" parameterType="LoginUserSession">
        UPDATE user_login_session
           SET logout_time = now(), logout_type = #{logout_type}
         WHERE user_email = #{user_email} AND login_session = #{login_session}
    </update>

    <insert id="insertUserLoginAttemptLog" parameterType="LoginAttemptLog">
        INSERT INTO user_login_attempt_log
                (
                     user_email, attempt_ip,
                     attempt_agent, attempt_referer, attempt_time,
                     error_code, error_message
                )
        VALUES (
                    #{user_email}, #{attempt_ip},
                    #{attempt_agent}, #{attempt_referer}, now(),
                    #{error_code}, #{error_message}
                )
    </insert>

    <update id="updateUserWebLog" parameterType="HashMap">
        UPDATE webservice_log
           SET user_token = #{after}, tracking = 'Y'
         WHERE user_token = #{before} AND tracking = 'N'
    </update>

    <update id="updateUserNewTokenId" parameterType="LoginUser">
        UPDATE user
           SET user_token_id = #{user_token_id}
         WHERE user_email = #{user_email}
    </update>

    <insert id="insertUserWebLog" parameterType="UserWebLog">
        INSERT INTO webservice_log
               (
                    user_token, user_email,
                    referer, request_uri, request_time,
                    request_host, session_id, tracking
               )
        VALUES (
                    #{user_token}, #{user_email},
                    #{referer}, #{request_uri}, now(),
                    #{request_host}, #{session_id}, #{tracking}
               )
    </insert>

    <insert id="insertUserLoginLog" parameterType="HashMap">
        INSERT INTO user_login_session
        (user_email, login_session, login_ip,
         login_agent, login_referer, login_time)
        VALUES (#{UserEmail}, #{SessionId}, #{LoginIp},
                #{UserAgent}, #{Referer}, now())
    </insert>

    <update id="updateUserLastLoginDate" parameterType="LoginUser">
        UPDATE user SET last_login_date = now()
        WHERE user_email = #{user_email} AND user_name = #{user_name}
    </update>

    <select id="selectUserCountById" parameterType="String" resultType="Integer">
        SELECT count(*) FROM user
         WHERE user_email = #{value}
    </select>

    <insert id="insertNewUser" parameterType="LoginUser">
        INSERT INTO user
                (
                 user_email, user_pass,
                 user_name, user_tel, user_mobile,
                 user_birthday, user_gender,
                 user_role, user_group_code, user_token_id,
                 user_join_date, user_modify_date,
                 <if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(naver_token)">
                 naver_token, naver_email, naver_join_date,
                 </if>
                 <if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(kakao_token)">
                 kakao_token, kakao_email, kakao_join_date,
                 </if>
                 <if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(google_token)">
                 google_token, google_email, google_join_date,
                 </if>
                 secondary_email, mailing_yn, push_message_yn,
                 privacy_retention_days
                )
        VALUES (
                #{user_email}, #{user_pass},
                #{user_name}, #{user_tel}, #{user_mobile},
                #{user_birthday}, #{user_gender},
                #{user_role}, #{user_group_code}, #{user_token_id},
                now(), now(),
                <if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(naver_token)">
                #{naver_token}, #{naver_email}, now(),
                </if>
                <if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(kakao_token)">
                #{kakao_token}, #{kakao_email}, now(),
                </if>
                <if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(google_token)">
                #{google_token}, #{google_email}, now(),
                </if>
                #{secondary_email}, #{mailing_yn}, #{push_message_yn},
                privacy_retention_days
               )
    </insert>
    
    <update id="updateUser" parameterType="LoginUser">
        Update `user` 
        <set>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_pass)">user_pass=#{user_pass},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_name)">user_name=#{user_name},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_tel)">user_tel=#{user_tel},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_mobile)">user_mobile=#{user_mobile},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_gender)" >	user_gender=#{user_gender},	</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_birthday)" >	user_birthday=#{user_birthday},	</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_role)">user_role=#{user_role},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_group_code)">user_group_code=#{user_group_code},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_token_id)">user_token_id=#{user_token_id},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_modify_date)">user_modify_date=#{user_modify_date},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(last_password_date)">last_password_date=#{last_password_date},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(naver_token)">naver_token=#{naver_token},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(naver_email)">naver_email=#{naver_email},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(naver_join_date)">naver_join_date=#{naver_join_date},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(kakao_token)">kakao_token=#{kakao_token},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(kakao_email)">kakao_email=#{kakao_email},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(kakao_join_date)">kakao_join_date=#{kakao_join_date},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(google_token)">google_token=#{google_token},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(google_email)">google_email=#{google_email},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(google_join_date)">google_join_date=#{google_join_date},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(secondary_email)">secondary_email=#{secondary_email},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(mailing_yn)">mailing_yn=#{mailing_yn},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(push_message_yn)">push_message_yn=#{push_message_yn},</if>
			<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(privacy_retention_days)">privacy_retention_days=#{privacy_retention_days},</if>
			last_login_date=now()
        </set>
        <where>
        	<if test="@kr.wayplus.hp.common.util.MybatisUtil@isNotEmpty(user_email)">and user_email=#{user_email}	</if>
        </where>
    </update>

    <select id="selectUserIdByUserInfo" parameterType="LoginUser" resultType="LoginUser">
        SELECT user_email, user_token_id, user_name, user_mobile
          FROM user
         WHERE user_name =  #{user_name} 
           AND REPLACE(user_mobile, '-', '') = REPLACE(#{user_mobile}, '-', '')
    </select>

    <select id="selectUserRePasswordByUserInfo" parameterType="LoginUser" resultType="LoginUser">
        SELECT user_email, user_token_id, user_name, user_mobile
          FROM user
         WHERE user_email = #{user_email} AND user_name =  #{user_name} AND REPLACE(user_mobile, '-', '') = REPLACE(#{user_mobile}, '-', '')
    </select>

    <select id="selectUserByUserToken" parameterType="LoginUser" resultType="LoginUser">
        SELECT user_email, user_token_id, user_name, user_mobile
          FROM user
         WHERE user_email = #{user_email} AND user_token_id = #{user_token_id}
    </select>

    <update id="updateUserPassword" parameterType="LoginUser">
        UPDATE user SET user_pass = #{user_pass}, last_password_date = now()
         WHERE user_email = #{user_email} AND user_token_id = #{user_token_id}
    </update>
</mapper>
