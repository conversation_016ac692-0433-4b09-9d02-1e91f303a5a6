/**
 * 관리자 사이드 네비게이션 JavaScript
 * - 사이드네비 토글, 메뉴 활성화, 서브메뉴 관리
 */
const AdminSideNav = {
    
    /**
     * 초기화
     */
    init() {
        this.bindEvents();
        this.setActiveMenu();
        this.initSubmenuState();
    },
    
    /**
     * 이벤트 바인딩
     */
    bindEvents() {
        // 사이드네비 토글 버튼 (모바일)
        const toggleBtn = document.getElementById('sidenavToggleBtn');
        const sidenavToggle = document.getElementById('sidenavToggle');
        const overlay = document.getElementById('sidenavOverlay');
        
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleSidenav());
        }
        
        if (sidenavToggle) {
            sidenavToggle.addEventListener('click', () => this.closeSidenav());
        }
        
        if (overlay) {
            overlay.addEventListener('click', () => this.closeSidenav());
        }
        
        // 서브메뉴가 있는 메뉴 아이템 클릭 처리
        const menuItems = document.querySelectorAll('[data-menu-type="submenu-parent"] > [data-menu-link]');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSubmenu(item.parentElement);
            });
        });

        // 메뉴 링크 클릭 시 활성화 처리
        const allMenuLinks = document.querySelectorAll('[data-menu-link], [data-submenu-link]');
        allMenuLinks.forEach(link => {
            if (link.getAttribute('href') && link.getAttribute('href') !== '#') {
                link.addEventListener('click', () => {
                    this.setActiveMenuItem(link);
                });
            }
        });
        
        // 윈도우 리사이즈 처리
        window.addEventListener('resize', () => this.handleResize());
    },
    
    /**
     * 사이드네비 토글
     */
    toggleSidenav() {
        const sidenav = document.getElementById('adminSidenav');
        const overlay = document.getElementById('sidenavOverlay');
        const mainWrapper = document.querySelector('.main-wrapper');
        
        if (sidenav && overlay) {
            const isVisible = sidenav.classList.contains('show');
            
            if (isVisible) {
                this.closeSidenav();
            } else {
                this.openSidenav();
            }
        }
    },
    
    /**
     * 사이드네비 열기
     */
    openSidenav() {
        const sidenav = document.getElementById('adminSidenav');
        const overlay = document.getElementById('sidenavOverlay');
        
        if (sidenav && overlay) {
            sidenav.classList.add('show');
            overlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    },
    
    /**
     * 사이드네비 닫기
     */
    closeSidenav() {
        const sidenav = document.getElementById('adminSidenav');
        const overlay = document.getElementById('sidenavOverlay');
        
        if (sidenav && overlay) {
            sidenav.classList.remove('show');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    },
    
    /**
     * 서브메뉴 토글
     */
    toggleSubmenu(menuItem) {
        const isOpen = menuItem.hasAttribute('data-submenu-open');

        // 다른 열린 서브메뉴 닫기
        const openMenuItems = document.querySelectorAll('[data-submenu-open]');
        openMenuItems.forEach(item => {
            if (item !== menuItem) {
                item.removeAttribute('data-submenu-open');
                item.classList.remove('open');
            }
        });

        // 현재 서브메뉴 토글
        if (isOpen) {
            menuItem.removeAttribute('data-submenu-open');
            menuItem.classList.remove('open');
        } else {
            menuItem.setAttribute('data-submenu-open', 'true');
            menuItem.classList.add('open');
        }
    },
    
    /**
     * 현재 페이지에 맞는 활성 메뉴 설정
     */
    setActiveMenu() {
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll('[data-menu-link], [data-submenu-link]');

        // 모든 활성 상태 제거
        menuLinks.forEach(link => {
            link.removeAttribute('data-menu-active');
            link.classList.remove('active');
        });

        // 현재 경로와 일치하는 메뉴 찾기
        let activeLink = null;
        let exactMatch = false;

        menuLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href !== '#') {
                if (href === currentPath) {
                    activeLink = link;
                    exactMatch = true;
                } else if (!exactMatch && currentPath.startsWith(href) && href.length > 1) {
                    activeLink = link;
                }
            }
        });

        if (activeLink) {
            this.setActiveMenuItem(activeLink);
        }
    },
    
    /**
     * 메뉴 아이템 활성화
     */
    setActiveMenuItem(link) {
        // 모든 활성 상태 제거
        const allLinks = document.querySelectorAll('[data-menu-link], [data-submenu-link]');
        allLinks.forEach(l => {
            l.removeAttribute('data-menu-active');
            l.classList.remove('active');
        });

        // 선택된 링크 활성화
        link.setAttribute('data-menu-active', 'true');
        link.classList.add('active');

        // 서브메뉴 링크인 경우 부모 메뉴도 활성화
        if (link.hasAttribute('data-submenu-link')) {
            const parentMenuItem = link.closest('[data-menu-type="submenu-parent"]');
            if (parentMenuItem) {
                const parentLink = parentMenuItem.querySelector('[data-menu-link]');
                if (parentLink) {
                    parentLink.setAttribute('data-menu-active', 'true');
                    parentLink.classList.add('active');
                }
                // 서브메뉴 열기
                parentMenuItem.setAttribute('data-submenu-open', 'true');
                parentMenuItem.classList.add('open');
            }
        }
    },
    
    /**
     * 서브메뉴 초기 상태 설정
     */
    initSubmenuState() {
        // 활성 서브메뉴가 있는 경우 부모 메뉴 열기
        const activeSubmenuLinks = document.querySelectorAll('[data-submenu-link][data-menu-active]');
        activeSubmenuLinks.forEach(link => {
            const parentMenuItem = link.closest('[data-menu-type="submenu-parent"]');
            if (parentMenuItem) {
                parentMenuItem.setAttribute('data-submenu-open', 'true');
                parentMenuItem.classList.add('open');
            }
        });
    },
    
    /**
     * 윈도우 리사이즈 처리
     */
    handleResize() {
        const width = window.innerWidth;
        
        // 데스크톱 크기에서는 오버레이 제거
        if (width >= 992) {
            this.closeSidenav();
        }
    },
    
    /**
     * 메뉴 상태 저장 (로컬 스토리지)
     */
    saveMenuState() {
        const openMenus = [];
        const openMenuItems = document.querySelectorAll('[data-submenu-open]');

        openMenuItems.forEach(item => {
            const menuLink = item.querySelector('[data-menu-link]');
            if (menuLink) {
                const menuId = menuLink.getAttribute('data-menu');
                if (menuId) {
                    openMenus.push(menuId);
                }
            }
        });

        localStorage.setItem('adminSidenavState', JSON.stringify(openMenus));
    },
    
    /**
     * 메뉴 상태 복원 (로컬 스토리지)
     */
    restoreMenuState() {
        try {
            const savedState = localStorage.getItem('adminSidenavState');
            if (savedState) {
                const openMenus = JSON.parse(savedState);
                
                openMenus.forEach(menuId => {
                    const menuLink = document.querySelector(`[data-menu="${menuId}"]`);
                    if (menuLink) {
                        const menuItem = menuLink.closest('[data-menu-type="submenu-parent"]');
                        if (menuItem) {
                            menuItem.setAttribute('data-submenu-open', 'true');
                            menuItem.classList.add('open');
                        }
                    }
                });
            }
        } catch (e) {
            console.warn('Failed to restore menu state:', e);
        }
    }
};

// 페이지 로드 시 초기화
document.addEventListener('DOMContentLoaded', function() {
    AdminSideNav.init();
    AdminSideNav.restoreMenuState();
});

// 페이지 언로드 시 상태 저장
window.addEventListener('beforeunload', function() {
    AdminSideNav.saveMenuState();
});
