<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <!-- 관리자 헤더 Fragment -->
    <header th:fragment="header" class="header">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <!-- 로고 -->
                <a class="navbar-brand" th:href="@{/manage}">
                    <img src="/images/logo.png" alt="한림공원" height="40" class="d-inline-block align-text-top">
                    <span class="ms-2 fw-bold text-primary">한림공원 관리시스템</span>
                </a>
                
                <!-- 모바일 메뉴 토글 버튼 -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <!-- 간소화된 헤더 - SideNav 사용으로 메뉴 제거 -->
                <div class="d-flex align-items-center">
                    <span class="text-muted small">SideNav를 통해 메뉴를 이용하세요</span>
                </div>
            </div>
        </nav>
        
        <!-- 알림 메시지 영역 -->
        <div class="container mt-3" th:if="${message != null or errorMessage != null}">
            <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${message != null}">
                <i class="fas fa-check-circle me-2"></i>
                <span th:text="${message}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${errorMessage != null}">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </header>
</body>
</html>
