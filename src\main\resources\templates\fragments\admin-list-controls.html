<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <!-- 관리자 리스트 공통 검색/필터/정렬 컨트롤 Fragment -->
    
    <!-- 
    사용법:
    <div th:replace="fragments/admin-list-controls :: controls(
        searchFields='categoryName:카테고리명,description:설명',
        filterOptions='status:상태:전체,ACTIVE:활성,INACTIVE:비활성',
        sortOptions='categoryName:카테고리명,createdAt:등록일',
        searchPlaceholder='카테고리명 또는 설명으로 검색',
        showDateRange=false,
        showNumberRange=false
    )"></div>
    -->
    
    <div th:fragment="controls(searchFields, filterOptions, sortOptions, searchPlaceholder, showDateRange, showNumberRange)" 
         class="admin-list-controls">
        
        <!-- 검색 및 필터 영역 -->
        <div class="search-filter-section">
            <div class="row g-3 align-items-end">
                
                <!-- 검색 종류 -->
                <div class="col-md-2" th:if="${searchFields != null and !searchFields.isEmpty()}">
                    <label for="searchFieldSelect" class="form-label">검색 종류</label>
                    <select class="form-select" id="searchFieldSelect" name="searchFieldSelect">
                        <option value="all">전체</option>
                        <th:block th:each="searchField : ${#strings.listSplit(searchFields, ',')}">
                            <th:block th:with="fieldParts=${#strings.listSplit(searchField, ':')}">
                                <option th:value="${fieldParts[0]}"
                                        th:text="${#lists.size(fieldParts) > 1 ? fieldParts[1] : fieldParts[0]}"
                                        th:selected="${searchCondition != null and searchCondition.selectedSearchField == fieldParts[0]}">
                                    필드명
                                </option>
                            </th:block>
                        </th:block>
                    </select>
                </div>

                <!-- 검색 키워드 -->
                <div class="col-md-4">
                    <label for="searchKeyword" class="form-label">검색어</label>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               id="searchKeyword"
                               name="searchKeyword"
                               th:placeholder="${searchPlaceholder != null ? searchPlaceholder : '검색어를 입력하세요'}"
                               th:value="${searchCondition != null ? searchCondition.searchKeyword : ''}"
                               data-search-fields="${searchFields}">
                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn" title="검색 초기화">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 동적 필터 옵션들 -->
                <th:block th:if="${filterOptions != null and !filterOptions.isEmpty()}">
                    <th:block th:each="filterOption : ${#strings.listSplit(filterOptions, '|')}">
                        <th:block th:with="filterParts=${#strings.listSplit(filterOption, ':')}">
                            <div class="col-md-2" th:if="${#lists.size(filterParts) >= 3}">
                                <label th:for="'filter_' + ${filterParts[0]}" 
                                       class="form-label" 
                                       th:text="${filterParts[1]}">필터</label>
                                <select class="form-select filter-select" 
                                        th:id="'filter_' + ${filterParts[0]}"
                                        th:name="'filter_' + ${filterParts[0]}"
                                        th:data-filter-field="${filterParts[0]}">
                                    <option value="" th:text="${filterParts[2]}">전체</option>
                                    <th:block th:each="option, iterStat : ${#strings.listSplit(filterOption, ':')}" 
                                              th:if="${iterStat.index >= 3 and iterStat.index % 2 == 1}">
                                        <option th:value="${#strings.listSplit(filterOption, ':')[iterStat.index]}"
                                                th:text="${#strings.listSplit(filterOption, ':')[iterStat.index + 1]}"
                                                th:selected="${searchCondition != null and searchCondition.filters != null and searchCondition.filters[filterParts[0]] == #strings.listSplit(filterOption, ':')[iterStat.index]}">
                                            옵션
                                        </option>
                                    </th:block>
                                </select>
                            </div>
                        </th:block>
                    </th:block>
                </th:block>
                
                <!-- 날짜 범위 검색 (옵션) -->
                <th:block th:if="${showDateRange != null and showDateRange}">
                    <div class="col-md-2">
                        <label for="dateFrom" class="form-label">시작일</label>
                        <input type="date" 
                               class="form-control" 
                               id="dateFrom" 
                               name="dateFrom"
                               th:value="${searchCondition != null and searchCondition.dateFrom != null ? searchCondition.dateFrom : ''}">
                    </div>
                    <div class="col-md-2">
                        <label for="dateTo" class="form-label">종료일</label>
                        <input type="date" 
                               class="form-control" 
                               id="dateTo" 
                               name="dateTo"
                               th:value="${searchCondition != null and searchCondition.dateTo != null ? searchCondition.dateTo : ''}">
                    </div>
                </th:block>
                
                <!-- 숫자 범위 검색 (옵션) -->
                <th:block th:if="${showNumberRange != null and showNumberRange}">
                    <div class="col-md-2">
                        <label for="numberFrom" class="form-label">최소값</label>
                        <input type="number" 
                               class="form-control" 
                               id="numberFrom" 
                               name="numberFrom"
                               th:value="${searchCondition != null and searchCondition.numberFrom != null ? searchCondition.numberFrom : ''}">
                    </div>
                    <div class="col-md-2">
                        <label for="numberTo" class="form-label">최대값</label>
                        <input type="number" 
                               class="form-control" 
                               id="numberTo" 
                               name="numberTo"
                               th:value="${searchCondition != null and searchCondition.numberTo != null ? searchCondition.numberTo : ''}">
                    </div>
                </th:block>
                
                <!-- 정렬 옵션 -->
                <div class="col-md-2" th:if="${sortOptions != null and !sortOptions.isEmpty()}">
                    <label for="sortField" class="form-label">정렬</label>
                    <select class="form-select" id="sortField" name="sortField">
                        <th:block th:each="sortOption : ${#strings.listSplit(sortOptions, ',')}">
                            <th:block th:with="sortParts=${#strings.listSplit(sortOption, ':')}">
                                <option th:if="${#lists.size(sortParts) >= 2}"
                                        th:value="${sortParts[0]}" 
                                        th:text="${sortParts[1]}"
                                        th:selected="${searchCondition != null and searchCondition.sortField == sortParts[0]}">
                                    정렬 옵션
                                </option>
                            </th:block>
                        </th:block>
                    </select>
                </div>
                
                <!-- 정렬 방향 -->
                <div class="col-md-2">
                    <label for="sortDirection" class="form-label">순서</label>
                    <select class="form-select" id="sortDirection" name="sortDirection">
                        <option value="DESC" th:selected="${searchCondition == null or searchCondition.sortDirection == 'DESC'}">내림차순</option>
                        <option value="ASC" th:selected="${searchCondition != null and searchCondition.sortDirection == 'ASC'}">오름차순</option>
                    </select>
                </div>
                
                <!-- 페이지 크기 -->
                <div class="col-md-2">
                    <label for="pageSize" class="form-label">표시 개수</label>
                    <select class="form-select" id="pageSize" name="pageSize">
                        <option value="10" th:selected="${searchCondition != null and searchCondition.size == 10}">10개</option>
                        <option value="20" th:selected="${searchCondition == null or searchCondition.size == 20}">20개</option>
                        <option value="50" th:selected="${searchCondition != null and searchCondition.size == 50}">50개</option>
                        <option value="100" th:selected="${searchCondition != null and searchCondition.size == 100}">100개</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 검색 결과 정보 -->
        <div class="search-result-info" th:if="${listResponse != null}">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <span class="result-count">
                        전체 <strong th:text="${listResponse.totalCount}">0</strong>개 중 
                        <strong th:text="${listResponse.startItemNumber}">0</strong> - 
                        <strong th:text="${listResponse.endItemNumber}">0</strong>개 표시
                    </span>
                    <span class="search-indicator" th:if="${listResponse.isSearchResult}">
                        <i class="fas fa-filter text-primary"></i> 검색 결과
                    </span>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="resetAllBtn">
                        <i class="fas fa-undo"></i> 전체 초기화
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 로딩 인디케이터 -->
        <div class="loading-indicator d-none" id="listLoadingIndicator">
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">로딩 중...</span>
                </div>
                <div class="mt-2">데이터를 불러오는 중입니다...</div>
            </div>
        </div>
        
        <!-- 숨겨진 폼 필드들 -->
        <input type="hidden" id="currentPage" name="currentPage" th:value="${searchCondition != null ? searchCondition.page : 1}">
        <input type="hidden" id="searchFieldsData" th:value="${searchFields}">
        <input type="hidden" id="dateField" name="dateField" th:value="${dateField != null ? dateField : ''}">
        <input type="hidden" id="numberField" name="numberField" th:value="${numberField != null ? numberField : ''}">
    </div>
    
    <!-- 페이징 네비게이션 Fragment -->
    <div th:fragment="pagination(listResponse)" class="admin-list-pagination" th:if="${listResponse != null and listResponse.totalCount > 0}">
        <nav aria-label="페이지 네비게이션">
            <ul class="pagination justify-content-center">
                <!-- 첫 페이지 -->
                <li class="page-item" th:classappend="${listResponse.isFirst} ? 'disabled'">
                    <a class="page-link" href="#" data-page="1" th:unless="${listResponse.isFirst}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                    <span class="page-link" th:if="${listResponse.isFirst}">
                        <i class="fas fa-angle-double-left"></i>
                    </span>
                </li>
                
                <!-- 이전 페이지 -->
                <li class="page-item" th:classappend="${!listResponse.hasPrevious} ? 'disabled'">
                    <a class="page-link" href="#" th:data-page="${listResponse.currentPage - 1}" th:if="${listResponse.hasPrevious}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                    <span class="page-link" th:unless="${listResponse.hasPrevious}">
                        <i class="fas fa-angle-left"></i>
                    </span>
                </li>
                
                <!-- 페이지 번호들 -->
                <li class="page-item" 
                    th:each="pageNum : ${listResponse.getPageNumbers(10)}"
                    th:classappend="${pageNum == listResponse.currentPage} ? 'active'">
                    <a class="page-link" href="#" th:data-page="${pageNum}" th:text="${pageNum}" th:unless="${pageNum == listResponse.currentPage}">1</a>
                    <span class="page-link" th:if="${pageNum == listResponse.currentPage}" th:text="${pageNum}">1</span>
                </li>
                
                <!-- 다음 페이지 -->
                <li class="page-item" th:classappend="${!listResponse.hasNext} ? 'disabled'">
                    <a class="page-link" href="#" th:data-page="${listResponse.currentPage + 1}" th:if="${listResponse.hasNext}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                    <span class="page-link" th:unless="${listResponse.hasNext}">
                        <i class="fas fa-angle-right"></i>
                    </span>
                </li>
                
                <!-- 마지막 페이지 -->
                <li class="page-item" th:classappend="${listResponse.isLast} ? 'disabled'">
                    <a class="page-link" href="#" th:data-page="${listResponse.totalPages}" th:unless="${listResponse.isLast}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                    <span class="page-link" th:if="${listResponse.isLast}">
                        <i class="fas fa-angle-double-right"></i>
                    </span>
                </li>
            </ul>
        </nav>
    </div>
    
    <!-- 빈 결과 표시 Fragment -->
    <div th:fragment="empty-result(message)" class="admin-list-empty">
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted" th:text="${message != null ? message : '조회된 데이터가 없습니다.'}">조회된 데이터가 없습니다.</h5>
            <p class="text-muted">검색 조건을 변경하거나 새로운 데이터를 등록해보세요.</p>
        </div>
    </div>

    <!-- 동적 테이블 컨테이너 Fragment -->
    <div th:fragment="table-container" class="admin-list-table-container">
        <div class="table-responsive">
            <div id="dynamicTableContent">
                <!-- 동적으로 로드되는 테이블 내용 -->
            </div>
        </div>
    </div>

    <!-- 에러 표시 Fragment -->
    <div th:fragment="error-result(errorMessage)" class="admin-list-error">
        <div class="alert alert-danger text-center" role="alert">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h5>데이터 로드 중 오류가 발생했습니다</h5>
            <p th:text="${errorMessage != null ? errorMessage : '알 수 없는 오류가 발생했습니다.'}">오류 메시지</p>
            <button type="button" class="btn btn-outline-danger" onclick="location.reload()">
                <i class="fas fa-redo"></i> 페이지 새로고침
            </button>
        </div>
    </div>
</body>
</html>
