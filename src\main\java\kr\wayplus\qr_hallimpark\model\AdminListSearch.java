package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 관리자 리스트 페이지 공통 검색 파라미터 Model
 * - 모든 관리자 리스트 페이지에서 재사용 가능한 범용적인 검색/필터/정렬/페이징 파라미터
 * - 설정 기반으로 각 페이지별 커스터마이징 가능
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminListSearch {

    // ========== 기본 검색 파라미터 ==========
    
    /**
     * 검색 키워드
     * - 여러 필드에 대한 통합 검색에 사용
     */
    private String searchKeyword;

    /**
     * 선택된 검색 필드
     * - 사용자가 선택한 단일 검색 필드
     * - 예: "category_name", "description", "all" (전체 검색)
     */
    private String selectedSearchField;

    /**
     * 검색 대상 필드 목록
     * - 검색 키워드가 적용될 데이터베이스 컬럼명 목록
     * - 예: ["category_name", "description"]
     */
    private List<String> searchFields;
    
    // ========== 필터링 파라미터 ==========
    
    /**
     * 필터 조건 맵
     * - key: 필터 필드명 (데이터베이스 컬럼명)
     * - value: 필터 값
     * - 예: {"status": "ACTIVE", "category_id": "1"}
     */
    private Map<String, String> filters;
    
    /**
     * 날짜 범위 필터 - 시작일
     */
    private LocalDate dateFrom;
    
    /**
     * 날짜 범위 필터 - 종료일
     */
    private LocalDate dateTo;
    
    /**
     * 날짜 범위 필터 대상 필드
     * - 날짜 범위 검색이 적용될 데이터베이스 컬럼명
     * - 예: "create_date", "last_update_date"
     */
    private String dateField;
    
    /**
     * 숫자 범위 필터 - 최소값
     */
    private Integer numberFrom;
    
    /**
     * 숫자 범위 필터 - 최대값
     */
    private Integer numberTo;
    
    /**
     * 숫자 범위 필터 대상 필드
     * - 숫자 범위 검색이 적용될 데이터베이스 컬럼명
     * - 예: "view_count", "difficulty_level"
     */
    private String numberField;
    
    // ========== 정렬 파라미터 ==========
    
    /**
     * 정렬 필드
     * - 정렬 기준이 되는 데이터베이스 컬럼명
     * - 기본값: "create_date"
     */
    @Builder.Default
    private String sortField = "create_date";
    
    /**
     * 정렬 방향
     * - ASC: 오름차순, DESC: 내림차순
     * - 기본값: "DESC"
     */
    @Builder.Default
    private String sortDirection = "DESC";
    
    // ========== 페이징 파라미터 ==========
    
    /**
     * 현재 페이지 번호
     * - 1부터 시작
     * - 기본값: 1
     */
    @Builder.Default
    private Integer page = 1;
    
    /**
     * 페이지당 표시할 항목 수
     * - 기본값: 20
     */
    @Builder.Default
    private Integer size = 20;
    
    /**
     * 데이터베이스 쿼리용 OFFSET 값
     * - (page - 1) * size
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 데이터베이스 쿼리용 LIMIT 값
     * - size와 동일
     */
    public Integer getLimit() {
        return size;
    }
    
    // ========== 메타 정보 ==========
    
    /**
     * 테이블명
     * - 동적 쿼리에서 사용할 테이블명
     */
    private String tableName;
    
    /**
     * 기본 WHERE 조건
     * - 모든 쿼리에 기본적으로 적용될 조건
     * - 예: "delete_yn = 'N'"
     */
    @Builder.Default
    private String baseCondition = "delete_yn = 'N'";
    
    // ========== 유틸리티 메서드 ==========
    
    /**
     * 검색 키워드가 있는지 확인
     */
    public boolean hasSearchKeyword() {
        return searchKeyword != null && !searchKeyword.trim().isEmpty();
    }
    
    /**
     * 필터가 있는지 확인
     */
    public boolean hasFilters() {
        return filters != null && !filters.isEmpty();
    }
    
    /**
     * 날짜 범위 필터가 있는지 확인
     */
    public boolean hasDateRange() {
        return dateFrom != null && dateTo != null && dateField != null && !dateField.trim().isEmpty();
    }
    
    /**
     * 숫자 범위 필터가 있는지 확인
     */
    public boolean hasNumberRange() {
        return numberFrom != null && numberTo != null && numberField != null && !numberField.trim().isEmpty();
    }
    
    /**
     * 정렬 조건이 유효한지 확인
     */
    public boolean hasValidSort() {
        return sortField != null && !sortField.trim().isEmpty() && 
               sortDirection != null && (sortDirection.equalsIgnoreCase("ASC") || sortDirection.equalsIgnoreCase("DESC"));
    }
}
