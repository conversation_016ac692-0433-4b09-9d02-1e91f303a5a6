/* 한림공원 QR 체험 - 공통 기본 스타일 */

/* CSS 변수 정의 */
:root {
    /* 브랜드 컬러 */
    --primary-color: #2E8B57;
    --primary-hover: #236B47;
    --secondary-color: #20B2AA;
    --secondary-hover: #1a9999;
    
    /* 시스템 컬러 */
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    /* 관리자 전용 컬러 */
    --admin-primary: #1a365d;
    --admin-secondary: #2d3748;
    
    /* 폰트 */
    --font-family: 'Noto Sans KR', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-base: 1rem;
    --line-height-base: 1.6;
    
    /* 간격 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* 테두리 반지름 */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 15px;
    --border-radius-xl: 20px;
    
    /* 그림자 */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-xl: 0 10px 25px rgba(0,0,0,0.1);
    --shadow-xxl: 0 20px 40px rgba(0,0,0,0.1);
    
    /* 전환 효과 */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* 기본 요소 스타일 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--dark-color);
    margin: 0;
    padding: 0;
}

/* 헤더 공통 스타일 */
.header {
    box-shadow: var(--shadow-sm);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--dark-color) !important;
    transition: color var(--transition-base);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* 메인 콘텐츠 */
.main-content {
    min-height: calc(100vh - 200px);
    padding-top: var(--spacing-xl);
}

/* 푸터 스타일 */
.footer {
    margin-top: auto;
}

.social-links a {
    transition: color var(--transition-base);
}

.social-links a:hover {
    color: var(--primary-color) !important;
}

/* 카드 공통 스타일 */
.card {
    border: none;
    border-radius: var(--border-radius-md);
    transition: transform var(--transition-base), box-shadow var(--transition-base);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* 버튼 공통 스타일 */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: all var(--transition-base);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-2px);
}

.btn-floating {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

/* 알림 공통 스타일 */
.alert {
    border-radius: var(--border-radius-sm);
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 폼 공통 스타일 */
.form-control,
.form-select {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e9ecef;
    padding: 0.75rem var(--spacing-md);
    transition: border-color var(--transition-base);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

/* 공통 애니메이션 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

/* 스크롤바 공통 스타일 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* 유틸리티 클래스 */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.shadow-custom {
    box-shadow: var(--shadow-md);
}

.shadow-lg-custom {
    box-shadow: var(--shadow-lg);
}

.border-radius-custom {
    border-radius: var(--border-radius-lg);
}

/* 로딩 스피너 */
.spinner-border-custom {
    color: var(--primary-color);
}

/* 반응형 기본 설정 */
@media (max-width: 768px) {
    .main-content {
        padding-top: var(--spacing-md);
    }
    
    .btn-lg {
        padding: 0.75rem var(--spacing-lg);
        font-size: var(--font-size-base);
    }
}

@media (max-width: 576px) {
    :root {
        --spacing-xl: 1.5rem;
        --spacing-xxl: 2rem;
    }
}
