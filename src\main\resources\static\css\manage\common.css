/* 한림공원 QR 체험 - 관리자 공통 스타일 */

/* 관리자 페이지 전용 설정 */
body {
    background-color: var(--light-color);
}

/* 관리자 헤더 스타일 */
.header {
    background: white !important;
}

/* 관리자 전용 대시보드 스타일 */
.dashboard-card {
    background: white;
}

.stat-card {
    background: var(--bg-gradient-primary);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    border: none;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
}

.admin-header {
    background: var(--bg-gradient-primary);
    color: white;
    padding: var(--spacing-xl) 0;
    margin-bottom: var(--spacing-xl);
}

/* 관리자 전용 테이블 스타일 */
.admin-table {
    background: white;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.admin-table th {
    background-color: var(--light-color);
    font-weight: 600;
    border: none;
    padding: var(--spacing-md);
}

.admin-table td {
    border: none;
    padding: var(--spacing-md);
    vertical-align: middle;
}

/* 관리자 전용 폼 스타일 */
.admin-form {
    background: white;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

/* 관리자 전용 사이드바 스타일 */

.sidebar {
    background: var(--admin-primary);
    min-height: 100vh;
    padding: var(--spacing-md) 0;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem var(--spacing-lg);
    border-radius: 0;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255,255,255,0.1);
}

.sidebar .nav-link.active {
    color: white;
    background-color: var(--primary-color);
}

/* 관리자 전용 반응형 디자인 */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }

    .admin-form {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 576px) {
    .sidebar {
        padding: var(--spacing-sm) 0;
    }
}
