<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>[%d{HH:mm:ss.SSS}][%-5level][%logger.%method:line%line] - %msg%n</pattern>
		</encoder>
	</appender>

	<springProfile name="dev">
		<appender name="dev-file" class="ch.qos.logback.core.rolling.RollingFileAppender">
			<file>c://logs//touristAnalystics.dev.log</file>
			<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
		    	<fileNamePattern>c://logs//touristAnalystics.%d{yyyy-MM-dd}.%i.dev.log</fileNamePattern>
		        <maxHistory>3</maxHistory>
		   		<maxFileSize>100MB</maxFileSize>
	  		</rollingPolicy>
	  		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
	      		<pattern>[%d{yyyy MMM dd HH:mm:ss.SSS}][%-5level][%logger.%method:line%line] - %msg%n</pattern>
	  		</encoder>
		</appender>
		<root level="debug">
			<appender-ref ref="console" />
			<!--<appender-ref ref="dev-file" />-->
		</root>
		<logger name="com.zaxxer.hikari" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
	<!--
		<logger name="org.apache" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework" level="off">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.security" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.servlet" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.web" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.web.servlet" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="_org.springframework.web" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.core" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.beans" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.context" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.jmx" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.boot" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.jdbc" level="off" additivity="false" >
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.expression" level="error">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.springframework.jdbc.support" level="debug">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.thymeleaf" level="error">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.thymeleaf.exceptions" level="error" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="org.mybatis" level="info" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="log4jdbc" level="off" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="jdbc" level="off">
			<appender-ref ref="console" />
		</logger>
		<logger name="jdbc.sqltiming" level="debug" >
			<appender-ref ref="console" />
		</logger>
		<logger name="kr.wayplus.hp" level="off">
			<appender-ref ref="console" />
		</logger>
		<logger name="kr.wayplus.hp.web" level="debug" additivity="false">
			<appender-ref ref="console" />
		</logger>
		<logger name="kr.wayplus.hp.mapper" level="debug" additivity="false">
			<appender-ref ref="console" />
		</logger>
	 -->
	</springProfile>

	<springProfile name="server">
		<appender name="server-file" class="ch.qos.logback.core.rolling.RollingFileAppender">
			<file>/logs/wayplus_hp.log</file>
			<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
		    	<fileNamePattern>/logs/wayplus_hp.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
		        <maxHistory>30</maxHistory>
		   		<maxFileSize>100MB</maxFileSize>
	  		</rollingPolicy>
	  		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
	      		<pattern>[%d{yyyy MMM dd HH:mm:ss.SSS}][%-5level][%logger.%method:line%line] - %msg%n</pattern>
	  		</encoder>
		</appender>
		<root level="info">
			<appender-ref ref="server-file" />
			<!--<appender-ref ref="dev-file" />-->
		</root>
<!--
		<logger name="com.zaxxer.hikari" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.apache" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework" level="off">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.security" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.servlet" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.web" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="_org.springframework.web" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.core" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.beans" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.context" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.jmx" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.boot" level="debug" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.jdbc" level="off" additivity="false" >
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.expression" level="error">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.springframework.jdbc.support" level="debug">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.thymeleaf" level="error">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.thymeleaf.exceptions" level="error" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="org.mybatis" level="info" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="log4jdbc" level="off" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="jdbc" level="off">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="jdbc.sqltiming" level="debug" >
			<appender-ref ref="server-file" />
		</logger>
		<logger name="kr.wayplus.hp" level="debug">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="kr.wayplus.hp.web" level="debug" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
		<logger name="kr.wayplus.hp.mapper" level="debug" additivity="false">
			<appender-ref ref="server-file" />
		</logger>
 -->
	</springProfile>
</configuration>