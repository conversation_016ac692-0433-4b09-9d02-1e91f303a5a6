package kr.wayplus.qr_hallimpark.service;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.common.service.impl.BaseAdminListService;
import kr.wayplus.qr_hallimpark.mapper.QuizCategoryMapper;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.QuizCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 문제 카테고리 서비스
 * - 문제 카테고리 CRUD 비즈니스 로직 처리
 * - 공통 리스트 기능 지원
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class QuizCategoryService extends BaseAdminListService<QuizCategory> {
    private final QuizCategoryMapper quizCategoryMapper;

    // ========== 공통 리스트 기능 구현 ==========

    @Override
    protected AdminListMapper<QuizCategory> getMapper() {
        return quizCategoryMapper;
    }

    @Override
    protected String getTableName() {
        return "quiz_category";
    }

    @Override
    protected String getDefaultSortField() {
        return "category_id"; // 최신 생성 순으로 정렬 (ID가 높을수록 최신)
    }

    @Override
    public AdminListSearch createDefaultSearchCondition() {
        return AdminListSearch.builder()
                .page(1)
                .size(20)
                .sortField("category_id")
                .sortDirection("DESC") // 내림차순으로 최신 항목이 위로
                .baseCondition("delete_yn = 'N'")
                .tableName(getTableName())
                .build();
    }

    @Override
    protected void validateDomainSpecificConditions(AdminListSearch searchCondition) {
        // 허용된 검색 필드 검증
        List<String> allowedSearchFields = Arrays.asList("category_name", "description");
        validateSearchFields(searchCondition, allowedSearchFields);

        // 허용된 정렬 필드 검증
        List<String> allowedSortFields = Arrays.asList("category_id", "category_name", "create_date", "last_update_date");
        validateSortField(searchCondition, allowedSortFields);
    }
    
    /**
     * 모든 문제 카테고리 목록 조회
     * @return 문제 카테고리 목록
     */
    public List<QuizCategory> findAllQuizCategories() {
        log.debug("Finding all quiz categories");
        return quizCategoryMapper.selectQuizCategoryList();
    }
    
    /**
     * 카테고리 ID로 문제 카테고리 조회
     * @param categoryId 카테고리 ID
     * @return 문제 카테고리 정보
     */
    public QuizCategory findQuizCategoryById(Long categoryId) {
        log.debug("Finding quiz category by ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }
        
        QuizCategory category = quizCategoryMapper.selectQuizCategoryById(categoryId);
        if (category == null) {
            throw new IllegalArgumentException("존재하지 않는 카테고리입니다. ID: " + categoryId);
        }
        
        return category;
    }
    
    /**
     * 카테고리명으로 문제 카테고리 조회
     * @param categoryName 카테고리명
     * @return 문제 카테고리 정보
     */
    public QuizCategory findQuizCategoryByName(String categoryName) {
        log.debug("Finding quiz category by name: {}", categoryName);
        
        if (categoryName == null || categoryName.trim().isEmpty()) {
            throw new IllegalArgumentException("카테고리명은 필수입니다.");
        }
        
        return quizCategoryMapper.selectQuizCategoryByName(categoryName);
    }
    
    /**
     * 문제 카테고리 등록
     * @param quizCategory 문제 카테고리 정보
     * @return 등록된 문제 카테고리 정보
     */
    @Transactional
    public QuizCategory createQuizCategory(QuizCategory quizCategory) {
        log.debug("Creating quiz category: {}", quizCategory);

        validateQuizCategory(quizCategory);
        validateHierarchyForCreate(quizCategory);

        // 카테고리명 중복 체크
        if (isDuplicateCategoryName(quizCategory.getCategoryName())) {
            throw new IllegalArgumentException("이미 존재하는 카테고리명입니다: " + quizCategory.getCategoryName());
        }

        int result = quizCategoryMapper.insertQuizCategory(quizCategory);
        if (result != 1) {
            throw new RuntimeException("문제 카테고리 등록에 실패했습니다.");
        }

        log.info("Quiz category created successfully. ID: {}", quizCategory.getCategoryId());
        return quizCategory;
    }
    
    /**
     * 문제 카테고리 수정
     * @param quizCategory 문제 카테고리 정보
     * @return 수정된 문제 카테고리 정보
     */
    @Transactional
    public QuizCategory updateQuizCategory(QuizCategory quizCategory) {
        log.debug("Updating quiz category: {}", quizCategory);

        validateQuizCategory(quizCategory);

        if (quizCategory.getCategoryId() == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }

        // 기존 카테고리 존재 여부 확인
        findQuizCategoryById(quizCategory.getCategoryId());

        // 계층형 구조 검증 (수정 시)
        validateHierarchyForUpdate(quizCategory);

        // 카테고리명 중복 체크 (자기 자신 제외)
        if (isDuplicateCategoryNameExcludeId(quizCategory.getCategoryName(), quizCategory.getCategoryId())) {
            throw new IllegalArgumentException("이미 존재하는 카테고리명입니다: " + quizCategory.getCategoryName());
        }
        
        int result = quizCategoryMapper.updateQuizCategory(quizCategory);
        if (result != 1) {
            throw new RuntimeException("문제 카테고리 수정에 실패했습니다.");
        }
        
        log.info("Quiz category updated successfully. ID: {}", quizCategory.getCategoryId());
        return quizCategory;
    }
    
    /**
     * 문제 카테고리 삭제 (소프트 삭제)
     * @param categoryId 카테고리 ID
     * @param deleteId 삭제자 ID
     */
    @Transactional
    public void deleteQuizCategory(Long categoryId, String deleteId) {
        log.debug("Deleting quiz category. ID: {}, DeleteId: {}", categoryId, deleteId);

        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }

        if (deleteId == null || deleteId.trim().isEmpty()) {
            throw new IllegalArgumentException("삭제자 ID는 필수입니다.");
        }

        // 기존 카테고리 존재 여부 확인
        findQuizCategoryById(categoryId);

        // 삭제 가능 여부 검증 (하위 카테고리 존재 시 삭제 불가)
        validateCategoryDeletion(categoryId);

        // 소프트 삭제를 위한 QuizCategory 객체 생성
        QuizCategory deleteCategory = QuizCategory.builder()
                .categoryId(categoryId)
                .deleteId(deleteId)
                .build();

        int result = quizCategoryMapper.deleteQuizCategory(deleteCategory);
        if (result != 1) {
            throw new RuntimeException("문제 카테고리 삭제에 실패했습니다.");
        }

        log.info("Quiz category deleted successfully. ID: {}", categoryId);
    }
    
    /**
     * 카테고리명 중복 체크
     * @param categoryName 카테고리명
     * @return 중복 여부
     */
    public boolean isDuplicateCategoryName(String categoryName) {
        if (categoryName == null || categoryName.trim().isEmpty()) {
            return false;
        }
        
        int count = quizCategoryMapper.countByCategoryName(categoryName);
        return count > 0;
    }
    
    /**
     * 카테고리명 중복 체크 (수정 시 자기 자신 제외)
     * @param categoryName 카테고리명
     * @param categoryId 제외할 카테고리 ID
     * @return 중복 여부
     */
    public boolean isDuplicateCategoryNameExcludeId(String categoryName, Long categoryId) {
        if (categoryName == null || categoryName.trim().isEmpty() || categoryId == null) {
            return false;
        }
        
        int count = quizCategoryMapper.countByCategoryNameExcludeId(categoryName, categoryId);
        return count > 0;
    }
    
    /**
     * 문제 카테고리 유효성 검증
     * @param quizCategory 문제 카테고리 정보
     */
    private void validateQuizCategory(QuizCategory quizCategory) {
        if (quizCategory == null) {
            throw new IllegalArgumentException("문제 카테고리 정보는 필수입니다.");
        }
        
        if (quizCategory.getCategoryName() == null || quizCategory.getCategoryName().trim().isEmpty()) {
            throw new IllegalArgumentException("카테고리명은 필수입니다.");
        }

        if (quizCategory.getCategoryName().length() > 100) {
            throw new IllegalArgumentException("카테고리명은 100자를 초과할 수 없습니다.");
        }
        
        if (quizCategory.getDescription() != null && quizCategory.getDescription().length() > 1000) {
            throw new IllegalArgumentException("카테고리 설명은 1000자를 초과할 수 없습니다.");
        }
    }

    // ========== 계층형 구조 관련 메서드 ==========

    /**
     * 최상위 카테고리 목록 조회
     * @return 최상위 카테고리 목록
     */
    public List<QuizCategory> findRootCategories() {
        log.debug("Finding root categories");
        return quizCategoryMapper.selectRootCategories();
    }

    /**
     * 특정 부모 카테고리의 자식 카테고리 목록 조회
     * @param parentId 부모 카테고리 ID
     * @return 자식 카테고리 목록
     */
    public List<QuizCategory> findChildCategories(Long parentId) {
        log.debug("Finding child categories for parent ID: {}", parentId);
        return quizCategoryMapper.selectChildCategories(parentId);
    }

    /**
     * 계층형 구조로 모든 카테고리 조회
     * @return 계층형 카테고리 목록
     */
    public List<QuizCategory> findCategoriesWithHierarchy() {
        log.debug("Finding categories with hierarchy");
        return quizCategoryMapper.selectCategoriesWithHierarchy();
    }

    /**
     * 부모 카테고리 선택용 목록 조회
     * @param excludeCategoryId 제외할 카테고리 ID (수정 시 자기 자신과 하위 카테고리 제외)
     * @return 부모 카테고리 선택 가능한 목록
     */
    public List<QuizCategory> findParentCategoryOptions(Long excludeCategoryId) {
        log.debug("Finding parent category options, excluding ID: {}", excludeCategoryId);
        return quizCategoryMapper.selectParentCategoryOptions(excludeCategoryId);
    }

    /**
     * 카테고리 경로 조회
     * @param categoryId 카테고리 ID
     * @return 카테고리 경로 (최상위부터 현재까지)
     */
    public List<QuizCategory> findCategoryPath(Long categoryId) {
        log.debug("Finding category path for ID: {}", categoryId);
        return quizCategoryMapper.selectCategoryPath(categoryId);
    }

    /**
     * 카테고리 깊이 조회
     * @param categoryId 카테고리 ID
     * @return 카테고리 깊이
     */
    public Integer findCategoryDepth(Long categoryId) {
        log.debug("Finding category depth for ID: {}", categoryId);
        return quizCategoryMapper.selectCategoryDepth(categoryId);
    }

    /**
     * 자식 카테고리 개수 조회
     * @param parentId 부모 카테고리 ID
     * @return 자식 카테고리 개수
     */
    public int countChildCategories(Long parentId) {
        log.debug("Counting child categories for parent ID: {}", parentId);
        return quizCategoryMapper.countChildCategories(parentId);
    }

    // ========== 계층형 구조 검증 메서드 ==========

    /**
     * 카테고리 등록 시 계층형 구조 검증
     * @param quizCategory 등록할 카테고리 정보
     */
    private void validateHierarchyForCreate(QuizCategory quizCategory) {
        if (quizCategory.getParentId() != null) {
            // 부모 카테고리 존재 여부 확인
            try {
                findQuizCategoryById(quizCategory.getParentId());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("존재하지 않는 부모 카테고리입니다.");
            }
        }
    }

    /**
     * 카테고리 수정 시 계층형 구조 검증
     * @param quizCategory 수정할 카테고리 정보
     */
    private void validateHierarchyForUpdate(QuizCategory quizCategory) {
        if (quizCategory.getParentId() != null) {
            // 부모 카테고리 존재 여부 확인
            try {
                findQuizCategoryById(quizCategory.getParentId());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("존재하지 않는 부모 카테고리입니다.");
            }

            // 자기 자신을 부모로 설정하는 것 방지
            if (quizCategory.getParentId().equals(quizCategory.getCategoryId())) {
                throw new IllegalArgumentException("자기 자신을 부모 카테고리로 설정할 수 없습니다.");
            }

            // 순환 참조 방지 (자신의 하위 카테고리를 부모로 설정하는 것 방지)
            List<Long> descendantIds = quizCategoryMapper.selectDescendantCategoryIds(quizCategory.getCategoryId());
            if (descendantIds.contains(quizCategory.getParentId())) {
                throw new IllegalArgumentException("하위 카테고리를 부모 카테고리로 설정할 수 없습니다.");
            }
        }
    }

    /**
     * 카테고리 삭제 가능 여부 검증 (자식 카테고리가 있으면 삭제 불가)
     * @param categoryId 삭제할 카테고리 ID
     */
    private void validateCategoryDeletion(Long categoryId) {
        int childCount = countChildCategories(categoryId);
        if (childCount > 0) {
            throw new IllegalArgumentException("하위 카테고리가 존재하는 카테고리는 삭제할 수 없습니다. 먼저 하위 카테고리를 삭제해주세요.");
        }
    }
}
