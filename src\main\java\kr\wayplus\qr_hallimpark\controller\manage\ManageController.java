package kr.wayplus.qr_hallimpark.controller.manage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 관리자 페이지 컨트롤러
 * - 관리자 전용 기능들
 */
@Controller
@RequestMapping("/manage")
public class ManageController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 관리자 메인 대시보드
     */
    @GetMapping({"", "/"})
    public String dashboard(Model model) {
        logger.debug("Admin dashboard requested");
        
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        
        // 페이지 메타 정보 설정
        model.addAttribute("pageTitle", "관리자 대시보드");
        model.addAttribute("pageDescription", "한림공원 QR 체험 관리자 페이지");
        model.addAttribute("username", auth.getName());
        
        return "manage/dashboard";
    }
}
