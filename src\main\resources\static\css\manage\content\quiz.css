/* 한림공원 QR 체험 - 문제 관리 스타일 */

/* 카테고리 필터 섹션 */
.category-filter-section {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
}

.filter-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.category-filter-container {
    width: 100%;
}

.category-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-btn {
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid #007bff;
    background: white;
    color: #007bff;
}

.category-btn:hover {
    background: #007bff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.category-btn.active {
    background: #007bff;
    color: white;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

/* 테이블 스타일 */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
    text-align: center;
    white-space: nowrap;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    text-align: center;
}

/* 문제명 스타일 */
.quiz-title {
    text-align: left;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
}

.quiz-title:hover {
    color: #007bff;
    text-decoration: underline;
}

/* 배지 스타일 */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 15px;
    font-weight: 500;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-secondary {
    background-color: #6c757d !important;
}

/* 버튼 그룹 */
.btn-group .btn {
    margin: 0;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 0.25rem;
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* 테이블 반응형 */
@media (max-width: 1200px) {
    .table thead th,
    .table tbody td {
        padding: 0.5rem 0.25rem;
        font-size: 0.85rem;
    }
    
    .quiz-title {
        font-size: 0.85rem;
    }
    
    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .category-buttons {
        justify-content: center;
    }
    
    .category-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .table-responsive {
        border-radius: 10px;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.4rem 0.2rem;
        font-size: 0.8rem;
    }
    
    .quiz-title {
        font-size: 0.8rem;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: 0.25rem !important;
        margin-bottom: 0.2rem;
    }
}

/* 빈 상태 스타일 */
.text-center.py-5 {
    padding: 3rem 1rem !important;
}

.text-center.py-5 .fa-inbox {
    color: #dee2e6;
}

.text-center.py-5 h5 {
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.text-center.py-5 p {
    color: #adb5bd;
    margin-bottom: 1.5rem;
}

/* 로딩 상태 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 상태별 텍스트 색상 */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-muted {
    color: #6c757d !important;
}

/* 문제 관리 페이지 헤더 스타일 */
.quiz-page-header {
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.quiz-page-header h2 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.quiz-page-header p {
    opacity: 0.9;
    margin-bottom: 0;
}

.quiz-page-header .btn-primary {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.quiz-page-header .btn-primary:hover {
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 커스텀 토글 버튼 */
.custom-toggle-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.custom-toggle-btn {
    position: relative;
    width: 60px;
    height: 28px;
    border: none;
    border-radius: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px;
}

.custom-toggle-btn.active {
    background-color: #007bff;
}

.custom-toggle-btn.inactive {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.custom-toggle-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    width: 24px;
    height: 24px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.custom-toggle-btn.active .toggle-slider {
    left: 2px;
}

.custom-toggle-btn.inactive .toggle-slider {
    left: calc(100% - 26px);
}

.toggle-text {
    position: absolute;
    font-size: 10px;
    font-weight: 600;
    white-space: nowrap;
    pointer-events: none;
    z-index: 1;
    width: 100%;
    text-align: center;
    line-height: 1;
}

.custom-toggle-btn.active .toggle-text {
    color: white;
}

.custom-toggle-btn.inactive .toggle-text {
    color: #6c757d;
}

/* 수정 버튼 스타일 */
.edit-quiz-btn {
    transition: all 0.3s ease;
    font-weight: 500;
}

.edit-quiz-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
