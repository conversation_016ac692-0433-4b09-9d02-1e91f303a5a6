package kr.wayplus.qr_hallimpark.config.handler;

import kr.wayplus.qr_hallimpark.model.LoginUser;
import kr.wayplus.qr_hallimpark.service.manage.ManageUserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Component;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;

/**
 * 관리자 로그인 성공 핸들러
 * - 관리자 전용 로그인 성공 시 처리 로직
 */
@Component
@RequiredArgsConstructor
public class ManageLoginSuccessHandler implements AuthenticationSuccessHandler {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ManageUserService manageUserService;

    @Value("${app.cookie.name:hallimpark}")
    private String cookieName;

    @Value("${app.cookie.domain:localhost}")
    private String cookieDomain;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, 
                                      Authentication authentication) throws IOException, ServletException {
        
        HttpSession session = request.getSession();
        LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        
        logger.debug("Admin login success. User: {}, Session: {}", user.getUser_email(), session.getId());
        
        // 세션에 관리자 로그인 정보 저장
        user.setUser_pass(""); // 보안을 위해 비밀번호 제거
        session.setAttribute("adminLogin", user);
        session.setAttribute("loginType", "ADMIN");
        
        // 기존 게스트 세션 제거
        if (session.getAttribute("guest") != null) {
            session.removeAttribute("guest");
        }

        // 저장된 요청 확인
        RequestCache requestCache = new HttpSessionRequestCache();
        SavedRequest savedRequest = requestCache.getRequest(request, response);

        // 로그인 로그 저장을 위한 파라미터 맵 생성
        HashMap<String, String> parameterMap = new HashMap<>();
        parameterMap.put("UserEmail", user.getUser_email());
        parameterMap.put("SessionId", session.getId());
        parameterMap.put("LoginIp", getClientIpAddress(request));
        parameterMap.put("UserAgent", request.getHeader("User-Agent"));
        parameterMap.put("Referer", request.getHeader("Referer"));

        // 관리자 로그인 로그 저장
        manageUserService.writeAdminLoginLog(parameterMap);
        
        // 관리자 마지막 로그인 일자 업데이트
        manageUserService.updateAdminLastLoginDate(user);

        // 관리자 아이디 저장 처리 (로그인 성공/실패와 관계없이 처리)
        handleRememberMe(user.getUser_email(), request, response);

        // 관리자 권한 확인 및 리다이렉션
        String redirectUrl = determineAdminRedirectUrl(user, savedRequest);
        logger.debug("Admin redirecting to: {}", redirectUrl);
        
        response.sendRedirect(redirectUrl);
    }

    /**
     * 관리자 리다이렉트 URL 결정
     * @param user 관리자 사용자
     * @param savedRequest 저장된 요청
     * @return 리다이렉트 URL
     */
    private String determineAdminRedirectUrl(LoginUser user, SavedRequest savedRequest) {
        // 저장된 요청이 있고 관리자 페이지인 경우 해당 페이지로 이동
        if (savedRequest != null && savedRequest.getRedirectUrl().contains("/manage")) {
            return savedRequest.getRedirectUrl();
        }

        // 관리자 권한 확인
        boolean isAdmin = user.getAuthorities().stream()
                .anyMatch(authority ->
                    authority.getAuthority().equals("ROLE_ADMIN"));

        if (isAdmin) {
            logger.debug("Admin user detected, redirecting to admin dashboard");
            return "/manage";
        } else {
            // 관리자 권한이 없는 경우 (이론적으로 발생하지 않아야 함)
            logger.warn("Non-admin user passed admin authentication: {}", user.getUser_email());
            return "/manage/login?error=y&code=001";
        }
    }

    /**
     * 관리자 아이디 저장 처리
     * @param email 이메일
     * @param request HTTP 요청
     * @param response HTTP 응답
     */
    private void handleRememberMe(String email, HttpServletRequest request, HttpServletResponse response) {
        try {
            String rememberParam = request.getParameter("remember");
            logger.debug("HandleRememberMe called - email: {}, remember: '{}'", email, rememberParam);

            if ("Y".equals(rememberParam)) {
                logger.debug("Creating remember me cookie for admin: {}", email);
                Cookie adminLoginCookie = new Cookie("hallimpark.admin.login.id", email);
                adminLoginCookie.setMaxAge(60 * 60 * 24 * 90); // 90일
                adminLoginCookie.setPath("/");
                adminLoginCookie.setHttpOnly(true);
                adminLoginCookie.setSecure(request.isSecure());
                response.addCookie(adminLoginCookie);
                logger.debug("Remember me cookie created successfully");
            } else {
                logger.debug("Removing existing remember me cookie");
                // 기존 쿠키 삭제
                Cookie adminLoginCookie = new Cookie("hallimpark.admin.login.id", "");
                adminLoginCookie.setMaxAge(-1);
                adminLoginCookie.setPath("/");
                response.addCookie(adminLoginCookie);
            }
        } catch (Exception e) {
            logger.error("Failed to handle remember me: {}", e.getMessage(), e);
        }
    }

    /**
     * 클라이언트 IP 주소 조회
     * @param request HTTP 요청
     * @return 클라이언트 IP 주소
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
