package kr.wayplus.qr_hallimpark.common.service;

import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;

/**
 * 관리자 리스트 페이지 공통 서비스 인터페이스
 * - 모든 관리자 리스트 페이지에서 구현해야 할 공통 메서드 정의
 * - 검색, 필터링, 정렬, 페이징 기능의 표준화
 */
public interface AdminListService<T> {
    
    /**
     * 검색 조건에 따른 리스트 조회
     * @param searchCondition 검색 조건
     * @return 페이징된 리스트 응답
     */
    AdminListResponse<T> findListWithConditions(AdminListSearch searchCondition);

    /**
     * 전체 데이터 개수 조회 (검색 조건 적용)
     * @param searchCondition 검색 조건
     * @return 전체 데이터 개수
     */
    Long countWithConditions(AdminListSearch searchCondition);

    /**
     * 기본 검색 조건 생성
     * - 각 서비스에서 기본값을 설정할 수 있도록 제공
     * @return 기본 검색 조건
     */
    default AdminListSearch createDefaultSearchCondition() {
        return AdminListSearch.builder()
                .page(1)
                .size(20)
                .sortField("create_date")
                .sortDirection("DESC")
                .baseCondition("delete_yn = 'N'")
                .build();
    }
    
    /**
     * 검색 조건 유효성 검사
     * @param searchCondition 검색 조건
     * @throws IllegalArgumentException 유효하지 않은 검색 조건인 경우
     */
    default void validateSearchCondition(AdminListSearch searchCondition) {
        if (searchCondition == null) {
            throw new IllegalArgumentException("검색 조건이 null입니다.");
        }

        if (searchCondition.getPage() == null || searchCondition.getPage() < 1) {
            searchCondition.setPage(1);
        }

        if (searchCondition.getSize() == null || searchCondition.getSize() < 1 || searchCondition.getSize() > 100) {
            searchCondition.setSize(20);
        }

        if (searchCondition.getSortField() == null || searchCondition.getSortField().trim().isEmpty()) {
            searchCondition.setSortField("create_date");
        }

        if (searchCondition.getSortDirection() == null ||
            (!searchCondition.getSortDirection().equalsIgnoreCase("ASC") &&
             !searchCondition.getSortDirection().equalsIgnoreCase("DESC"))) {
            searchCondition.setSortDirection("DESC");
        }

        if (searchCondition.getBaseCondition() == null || searchCondition.getBaseCondition().trim().isEmpty()) {
            searchCondition.setBaseCondition("delete_yn = 'N'");
        }
    }
}
