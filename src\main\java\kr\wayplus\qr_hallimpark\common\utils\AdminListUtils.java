package kr.wayplus.qr_hallimpark.common.utils;

import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 관리자 리스트 페이지 공통 유틸리티 클래스
 * - 검색 조건 파싱, 변환, 유효성 검사 등의 유틸리티 메서드 제공
 */
@Slf4j
public class AdminListUtils {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 검색 필드 문자열을 List로 파싱
     * @param searchFieldsStr 검색 필드 문자열 (예: "categoryName:카테고리명,description:설명")
     * @return 검색 필드 목록
     */
    public static List<String> parseSearchFields(String searchFieldsStr) {
        if (searchFieldsStr == null || searchFieldsStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return Arrays.stream(searchFieldsStr.split(","))
                .map(field -> {
                    String[] parts = field.split(":");
                    return parts[0].trim(); // 필드명만 반환 (라벨은 제외)
                })
                .filter(field -> !field.isEmpty())
                .collect(Collectors.toList());
    }
    
    /**
     * 필터 옵션 문자열을 Map으로 파싱
     * @param filterOptionsStr 필터 옵션 문자열 (예: "status:상태:전체,ACTIVE:활성,INACTIVE:비활성")
     * @return 필터 옵션 맵 (key: 필드명, value: 옵션 맵)
     */
    public static Map<String, Map<String, String>> parseFilterOptions(String filterOptionsStr) {
        Map<String, Map<String, String>> filterOptions = new HashMap<>();
        
        if (filterOptionsStr == null || filterOptionsStr.trim().isEmpty()) {
            return filterOptions;
        }
        
        String[] filterGroups = filterOptionsStr.split("\\|");
        
        for (String filterGroup : filterGroups) {
            String[] parts = filterGroup.split(":");
            if (parts.length >= 3) {
                String fieldName = parts[0].trim();
                Map<String, String> options = new HashMap<>();
                
                // 기본 옵션 (전체)
                options.put("", parts[2].trim());
                
                // 나머지 옵션들
                for (int i = 3; i < parts.length; i += 2) {
                    if (i + 1 < parts.length) {
                        options.put(parts[i].trim(), parts[i + 1].trim());
                    }
                }
                
                filterOptions.put(fieldName, options);
            }
        }
        
        return filterOptions;
    }
    
    /**
     * 정렬 옵션 문자열을 Map으로 파싱
     * @param sortOptionsStr 정렬 옵션 문자열 (예: "categoryName:카테고리명,createdAt:등록일")
     * @return 정렬 옵션 맵 (key: 필드명, value: 라벨)
     */
    public static Map<String, String> parseSortOptions(String sortOptionsStr) {
        Map<String, String> sortOptions = new LinkedHashMap<>();
        
        if (sortOptionsStr == null || sortOptionsStr.trim().isEmpty()) {
            return sortOptions;
        }
        
        Arrays.stream(sortOptionsStr.split(","))
                .forEach(option -> {
                    String[] parts = option.split(":");
                    if (parts.length >= 2) {
                        sortOptions.put(parts[0].trim(), parts[1].trim());
                    }
                });
        
        return sortOptions;
    }
    
    /**
     * 요청 파라미터에서 검색 조건 생성
     * @param parameterMap 요청 파라미터 맵
     * @return 검색 조건 객체
     */
    public static AdminListSearch createSearchConditionFromParameters(Map<String, String[]> parameterMap) {
        AdminListSearch.AdminListSearchBuilder builder = AdminListSearch.builder();
        
        // 기본 파라미터들
        builder.searchKeyword(getParameterValue(parameterMap, "searchKeyword"));
        builder.page(getIntParameterValue(parameterMap, "page", 1));
        builder.size(getIntParameterValue(parameterMap, "size", 20));
        builder.sortField(getParameterValue(parameterMap, "sortField", "create_date"));
        builder.sortDirection(getParameterValue(parameterMap, "sortDirection", "DESC"));
        
        // 날짜 범위
        String dateFromStr = getParameterValue(parameterMap, "dateFrom");
        String dateToStr = getParameterValue(parameterMap, "dateTo");
        if (dateFromStr != null && dateToStr != null) {
            try {
                builder.dateFrom(LocalDate.parse(dateFromStr, DATE_FORMATTER));
                builder.dateTo(LocalDate.parse(dateToStr, DATE_FORMATTER));
                builder.dateField(getParameterValue(parameterMap, "dateField"));
            } catch (DateTimeParseException e) {
                log.warn("Invalid date format: {} or {}", dateFromStr, dateToStr);
            }
        }
        
        // 숫자 범위
        Integer numberFrom = getIntParameterValue(parameterMap, "numberFrom", null);
        Integer numberTo = getIntParameterValue(parameterMap, "numberTo", null);
        if (numberFrom != null && numberTo != null) {
            builder.numberFrom(numberFrom);
            builder.numberTo(numberTo);
            builder.numberField(getParameterValue(parameterMap, "numberField"));
        }
        
        // 필터들
        Map<String, String> filters = new HashMap<>();
        parameterMap.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("filter_"))
                .forEach(entry -> {
                    String filterKey = entry.getKey().substring(7); // "filter_" 제거
                    String filterValue = getParameterValue(parameterMap, entry.getKey());
                    if (filterValue != null && !filterValue.trim().isEmpty()) {
                        filters.put(filterKey, filterValue);
                    }
                });
        
        if (!filters.isEmpty()) {
            builder.filters(filters);
        }
        
        return builder.build();
    }
    
    /**
     * 파라미터 맵에서 문자열 값 추출
     */
    private static String getParameterValue(Map<String, String[]> parameterMap, String key) {
        return getParameterValue(parameterMap, key, null);
    }
    
    /**
     * 파라미터 맵에서 문자열 값 추출 (기본값 포함)
     */
    private static String getParameterValue(Map<String, String[]> parameterMap, String key, String defaultValue) {
        String[] values = parameterMap.get(key);
        if (values != null && values.length > 0 && values[0] != null && !values[0].trim().isEmpty()) {
            return values[0].trim();
        }
        return defaultValue;
    }
    
    /**
     * 파라미터 맵에서 정수 값 추출
     */
    private static Integer getIntParameterValue(Map<String, String[]> parameterMap, String key, Integer defaultValue) {
        String value = getParameterValue(parameterMap, key);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                log.warn("Invalid integer parameter: {} = {}", key, value);
            }
        }
        return defaultValue;
    }
    
    /**
     * 검색 조건을 URL 쿼리 스트링으로 변환
     * @param searchCondition 검색 조건
     * @return URL 쿼리 스트링
     */
    public static String toQueryString(AdminListSearch searchCondition) {
        if (searchCondition == null) {
            return "";
        }
        
        List<String> params = new ArrayList<>();
        
        if (searchCondition.getSearchKeyword() != null && !searchCondition.getSearchKeyword().trim().isEmpty()) {
            params.add("searchKeyword=" + encodeParameter(searchCondition.getSearchKeyword()));
        }
        
        if (searchCondition.getPage() != null && searchCondition.getPage() > 1) {
            params.add("page=" + searchCondition.getPage());
        }
        
        if (searchCondition.getSize() != null && searchCondition.getSize() != 20) {
            params.add("size=" + searchCondition.getSize());
        }
        
        if (searchCondition.getSortField() != null && !"create_date".equals(searchCondition.getSortField())) {
            params.add("sortField=" + encodeParameter(searchCondition.getSortField()));
        }
        
        if (searchCondition.getSortDirection() != null && !"DESC".equals(searchCondition.getSortDirection())) {
            params.add("sortDirection=" + searchCondition.getSortDirection());
        }
        
        if (searchCondition.getDateFrom() != null) {
            params.add("dateFrom=" + searchCondition.getDateFrom().format(DATE_FORMATTER));
        }
        
        if (searchCondition.getDateTo() != null) {
            params.add("dateTo=" + searchCondition.getDateTo().format(DATE_FORMATTER));
        }
        
        if (searchCondition.getDateField() != null) {
            params.add("dateField=" + encodeParameter(searchCondition.getDateField()));
        }
        
        if (searchCondition.getNumberFrom() != null) {
            params.add("numberFrom=" + searchCondition.getNumberFrom());
        }
        
        if (searchCondition.getNumberTo() != null) {
            params.add("numberTo=" + searchCondition.getNumberTo());
        }
        
        if (searchCondition.getNumberField() != null) {
            params.add("numberField=" + encodeParameter(searchCondition.getNumberField()));
        }
        
        if (searchCondition.getFilters() != null) {
            searchCondition.getFilters().entrySet().forEach(entry -> {
                params.add("filter_" + entry.getKey() + "=" + encodeParameter(entry.getValue()));
            });
        }
        
        return String.join("&", params);
    }
    
    /**
     * URL 파라미터 인코딩
     */
    private static String encodeParameter(String value) {
        try {
            return java.net.URLEncoder.encode(value, "UTF-8");
        } catch (java.io.UnsupportedEncodingException e) {
            return value;
        }
    }
    
    /**
     * 검색 조건 유효성 검사
     * @param searchCondition 검색 조건
     * @param allowedSearchFields 허용된 검색 필드 목록
     * @param allowedSortFields 허용된 정렬 필드 목록
     * @param allowedFilterFields 허용된 필터 필드 목록
     */
    public static void validateSearchCondition(AdminListSearch searchCondition, 
                                             List<String> allowedSearchFields,
                                             List<String> allowedSortFields,
                                             List<String> allowedFilterFields) {
        if (searchCondition == null) {
            return;
        }
        
        // 검색 필드 유효성 검사
        if (searchCondition.getSearchFields() != null && allowedSearchFields != null) {
            List<String> validFields = searchCondition.getSearchFields().stream()
                    .filter(allowedSearchFields::contains)
                    .collect(Collectors.toList());
            searchCondition.setSearchFields(validFields.isEmpty() ? null : validFields);
        }
        
        // 정렬 필드 유효성 검사
        if (searchCondition.getSortField() != null && allowedSortFields != null && 
            !allowedSortFields.contains(searchCondition.getSortField())) {
            searchCondition.setSortField("create_date");
        }
        
        // 필터 필드 유효성 검사
        if (searchCondition.getFilters() != null && allowedFilterFields != null) {
            Map<String, String> validFilters = searchCondition.getFilters().entrySet().stream()
                    .filter(entry -> allowedFilterFields.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            searchCondition.setFilters(validFilters.isEmpty() ? null : validFilters);
        }
    }
}
