package kr.wayplus.qr_hallimpark.mapper;

import kr.wayplus.qr_hallimpark.model.LoginAttemptLog;
import kr.wayplus.qr_hallimpark.model.LoginUser;
import kr.wayplus.qr_hallimpark.model.LoginUserSession;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.HashMap;

/**
 * 관리자 사용자 매퍼
 * - 관리자 권한 사용자 조회 및 인증 관련 DB 처리
 */
@Mapper
@Repository
public interface ManageUserMapper {
    
    /**
     * 관리자 권한 사용자 조회 (ADMIN, MANAGER 권한만)
     * @param username 사용자 이메일
     * @return 관리자 권한 사용자 정보
     */
    LoginUser selectAdminUserByUserid(String username);
    
    /**
     * 관리자 로그인 시도 로그 저장
     * @param attemptLog 로그인 시도 로그
     */
    void insertAdminLoginAttemptLog(LoginAttemptLog attemptLog);
    
    /**
     * 관리자 로그인 로그 저장
     * @param parameterMap 로그인 정보
     */
    void insertAdminLoginLog(HashMap<String, String> parameterMap);
    
    /**
     * 관리자 마지막 로그인 일자 업데이트
     * @param user 사용자 정보
     */
    void updateAdminLastLoginDate(LoginUser user);
    
    /**
     * 관리자 세션 로그아웃 처리
     * @param loginUserSession 세션 정보
     */
    void updateAdminSessionLogout(LoginUserSession loginUserSession);
    
    /**
     * 관리자 권한 사용자 수 조회
     * @param id 사용자 이메일
     * @return 관리자 권한 사용자 수
     */
    int selectAdminUserCountById(String id);
}
