package kr.wayplus.qr_hallimpark.service.manage;

import kr.wayplus.qr_hallimpark.common.utils.CustomBcryptPasswordEncoder;
import kr.wayplus.qr_hallimpark.mapper.ManageUserMapper;
import kr.wayplus.qr_hallimpark.model.LoginAttemptLog;
import kr.wayplus.qr_hallimpark.model.LoginUser;
import kr.wayplus.qr_hallimpark.model.LoginUserSession;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 관리자 사용자 서비스
 * - 관리자 전용 인증 로직과 DB 검증 기능
 */
@Service
@RequiredArgsConstructor
public class ManageUserService implements UserDetailsService {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ManageUserMapper manageUserMapper;

    /**
     * 관리자 사용자 인증 (Spring Security UserDetailsService 구현)
     * @param username 사용자 이메일
     * @return 관리자 권한 사용자 정보
     * @throws UsernameNotFoundException 관리자 권한 사용자가 없을 경우
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("Admin authentication attempt. Username: {}", username);
        
        LoginUser user = manageUserMapper.selectAdminUserByUserid(username);
        
        if (user == null) {
            logger.warn("Admin user not found or insufficient privileges: {}", username);
            throw new UsernameNotFoundException("관리자 권한이 없거나 존재하지 않는 사용자입니다: " + username);
        }

        List<GrantedAuthority> roles = new ArrayList<>();
        // 교환권 QR 승인 가능한 사용자는 ADMIN 권한 부여
        roles.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        user.setAuthorities(roles);

        return user;
    }

    /**
     * 관리자 로그인 시도 로그 저장
     * @param attemptLog 로그인 시도 로그
     */
    public void writeAdminLoginAttemptLog(LoginAttemptLog attemptLog) {
        try {
            manageUserMapper.insertAdminLoginAttemptLog(attemptLog);
            logger.debug("Admin login attempt log saved for user: {}", attemptLog.getUser_email());
        } catch (Exception e) {
            logger.error("Failed to save admin login attempt log: {}", e.getMessage(), e);
        }
    }

    /**
     * 관리자 로그인 로그 저장
     * @param parameterMap 로그인 정보
     */
    public void writeAdminLoginLog(HashMap<String, String> parameterMap) {
        try {
            manageUserMapper.insertAdminLoginLog(parameterMap);
            logger.debug("Admin login log saved for user: {}", parameterMap.get("UserEmail"));
        } catch (Exception e) {
            logger.error("Failed to save admin login log: {}", e.getMessage(), e);
        }
    }

    /**
     * 관리자 마지막 로그인 일자 업데이트
     * @param user 사용자 정보
     */
    public void updateAdminLastLoginDate(LoginUser user) {
        try {
            manageUserMapper.updateAdminLastLoginDate(user);
            logger.debug("Admin last login date updated for user: {}", user.getUser_email());
        } catch (Exception e) {
            logger.error("Failed to update admin last login date: {}", e.getMessage(), e);
        }
    }

    /**
     * 관리자 세션 로그아웃 처리
     * @param loginUserSession 세션 정보
     */
    public void updateAdminSessionLogout(LoginUserSession loginUserSession) {
        try {
            manageUserMapper.updateAdminSessionLogout(loginUserSession);
            logger.debug("Admin session logout processed for user: {}", loginUserSession.getUser_email());
        } catch (Exception e) {
            logger.error("Failed to process admin session logout: {}", e.getMessage(), e);
        }
    }

    /**
     * 관리자 권한 사용자 수 조회
     * @param id 사용자 이메일
     * @return 관리자 권한 사용자 수
     */
    public int findAdminUserCountById(String id) {
        try {
            return manageUserMapper.selectAdminUserCountById(id);
        } catch (Exception e) {
            logger.error("Failed to find admin user count: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 관리자 사용자 조회
     * @param username 사용자 이메일
     * @return 관리자 권한 사용자 정보
     */
    public LoginUser findAdminUserByUsername(String username) {
        try {
            return manageUserMapper.selectAdminUserByUserid(username);
        } catch (Exception e) {
            logger.error("Failed to find admin user: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 비밀번호 검증
     * @param rawPassword 입력된 비밀번호
     * @param encodedPassword 암호화된 비밀번호
     * @return 비밀번호 일치 여부
     */
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        return verifyPassword(rawPassword, encodedPassword, null);
    }

    /**
     * 비밀번호 검증 (세션을 통한 AES 복호화 지원)
     * @param rawPassword 입력된 비밀번호
     * @param encodedPassword 암호화된 비밀번호
     * @param session HTTP 세션 (AES 복호화용 키 조회)
     * @return 비밀번호 일치 여부
     */
    public boolean verifyPassword(String rawPassword, String encodedPassword, jakarta.servlet.http.HttpSession session) {
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword, session);
    }
}
