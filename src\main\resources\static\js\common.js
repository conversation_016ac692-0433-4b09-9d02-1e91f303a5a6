/**
 * 한림공원 QR 체험 - 관리자 공통 JavaScript
 */

// DOM 로드 완료 시 실행
document.addEventListener('DOMContentLoaded', function() {
    // 페이지 로드 애니메이션
    initPageAnimation();
    
    // 알림 메시지 자동 숨김
    initAlertAutoHide();
    
    // 툴팁 초기화
    initTooltips();
    
    // 외부 링크 처리
    initExternalLinks();
    
    // 폼 유효성 검사
    initFormValidation();

    // 관리자 테이블 초기화
    initAdminTables();

    // 관리자 차트 초기화
    initAdminCharts();
});

/**
 * 페이지 로드 애니메이션 초기화
 */
function initPageAnimation() {
    // 페이지 콘텐츠에 fade-in 애니메이션 적용
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
}

/**
 * 알림 메시지 자동 숨김 초기화
 */
function initAlertAutoHide() {
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        // 5초 후 자동으로 알림 숨김
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * 툴팁 초기화
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 외부 링크 처리
 */
function initExternalLinks() {
    const externalLinks = document.querySelectorAll('a[href^="http"]:not([href*="' + window.location.hostname + '"])');
    externalLinks.forEach(function(link) {
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');
    });
}

/**
 * 폼 유효성 검사 초기화
 */
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * 관리자 테이블 초기화
 */
function initAdminTables() {
    // DataTables 초기화 (추후 구현)
    const adminTables = document.querySelectorAll('.admin-table');
    adminTables.forEach(function(table) {
        // 테이블 기능 초기화 예정
    });
}

/**
 * 관리자 차트 초기화
 */
function initAdminCharts() {
    // Chart.js 초기화 (추후 구현)
    const chartElements = document.querySelectorAll('.admin-chart');
    chartElements.forEach(function(element) {
        // 차트 초기화 예정
    });
}

/**
 * 로딩 스피너 표시
 */
function showLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        const originalContent = element.innerHTML;
        element.setAttribute('data-original-content', originalContent);
        element.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>로딩 중...';
        element.disabled = true;
    }
}

/**
 * 로딩 스피너 숨김
 */
function hideLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        const originalContent = element.getAttribute('data-original-content');
        if (originalContent) {
            element.innerHTML = originalContent;
            element.removeAttribute('data-original-content');
        }
        element.disabled = false;
    }
}

/**
 * 성공 메시지 표시
 */
function showSuccessMessage(message, container = '.main-content') {
    showMessage(message, 'success', container);
}

/**
 * 에러 메시지 표시
 */
function showErrorMessage(message, container = '.main-content') {
    showMessage(message, 'danger', container);
}

/**
 * 경고 메시지 표시
 */
function showWarningMessage(message, container = '.main-content') {
    showMessage(message, 'warning', container);
}

/**
 * 정보 메시지 표시
 */
function showInfoMessage(message, container = '.main-content') {
    showMessage(message, 'info', container);
}

/**
 * 메시지 표시 (내부 함수)
 */
function showMessage(message, type, container) {
    const containerElement = document.querySelector(container);
    if (!containerElement) return;
    
    // 기존 메시지 제거
    const existingAlerts = containerElement.querySelectorAll('.alert-dynamic');
    existingAlerts.forEach(alert => alert.remove());
    
    // 새 메시지 생성
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-dynamic`;
    alertDiv.innerHTML = `
        <i class="fas fa-${getIconForType(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 컨테이너 맨 위에 삽입
    containerElement.insertBefore(alertDiv, containerElement.firstChild);
    
    // 5초 후 자동 제거
    setTimeout(() => {
        if (alertDiv.parentNode) {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }
    }, 5000);
}

/**
 * 메시지 타입에 따른 아이콘 반환
 */
function getIconForType(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * 확인 대화상자
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        if (typeof callback === 'function') {
            callback();
        }
        return true;
    }
    return false;
}

/**
 * 페이지 새로고침
 */
function refreshPage() {
    window.location.reload();
}

/**
 * 페이지 이동
 */
function navigateTo(url) {
    window.location.href = url;
}

/**
 * 뒤로 가기
 */
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        navigateTo('/');
    }
}

/**
 * 맨 위로 스크롤
 */
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

/**
 * 요소로 스크롤
 */
function scrollToElement(selector) {
    const element = document.querySelector(selector);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

/**
 * 클립보드에 텍스트 복사
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showSuccessMessage('클립보드에 복사되었습니다.');
        return true;
    } catch (err) {
        console.error('클립보드 복사 실패:', err);
        showErrorMessage('클립보드 복사에 실패했습니다.');
        return false;
    }
}

/**
 * 디바운스 함수
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 쓰로틀 함수
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 전역 객체에 공통 함수들 등록
window.HallimParkAdmin = {
    showLoading,
    hideLoading,
    showSuccessMessage,
    showErrorMessage,
    showWarningMessage,
    showInfoMessage,
    confirmAction,
    refreshPage,
    navigateTo,
    goBack,
    scrollToTop,
    scrollToElement,
    copyToClipboard,
    debounce,
    throttle
};
