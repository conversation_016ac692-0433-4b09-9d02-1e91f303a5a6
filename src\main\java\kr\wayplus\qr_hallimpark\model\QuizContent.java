package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * 문제 콘텐츠 모델
 * - quiz_content 테이블에 대응
 * - 다국어 지원을 위한 콘텐츠 정보
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuizContent {

    /**
     * 콘텐츠 고유 ID
     */
    private Long quizContentId;

    /**
     * 문제 ID (FK)
     */
    private Long quizId;

    /**
     * 언어 코드 (ko, en, ja, zh)
     */
    private String langCode;

    /**
     * 문제 질문 내용
     */
    private String question;

    /**
     * 문제 보기 (JSON 형태, 객관식 등에서 사용)
     */
    private String options;

    /**
     * 생성자 (user_email)
     */
    private String createId;

    /**
     * 생성일시
     */
    private LocalDateTime createDate;

    /**
     * 최종수정자 (user_email)
     */
    private String lastUpdateId;

    /**
     * 최종수정일시
     */
    private LocalDateTime lastUpdateDate;

    /**
     * 삭제여부
     */
    private String deleteYn;

    /**
     * 삭제자 (user_email)
     */
    private String deleteId;

    /**
     * 삭제일시
     */
    private LocalDateTime deleteDate;

    // ========== 유틸리티 메서드 ==========

    /**
     * 활성 상태인지 확인
     * @return 활성 상태 여부
     */
    public boolean isActive() {
        return "N".equals(this.deleteYn);
    }

    /**
     * 언어 코드를 한국어로 변환
     * @return 한국어 언어명
     */
    public String getLanguageDisplay() {
        if (this.langCode == null) {
            return "알 수 없음";
        }
        
        switch (this.langCode) {
            case "ko":
                return "한국어";
            case "en":
                return "영어";
            case "ja":
                return "일본어";
            case "zh":
                return "중국어";
            default:
                return this.langCode;
        }
    }

    /**
     * 한국어 콘텐츠인지 확인
     * @return 한국어 여부
     */
    public boolean isKorean() {
        return "ko".equals(this.langCode);
    }

    /**
     * 영어 콘텐츠인지 확인
     * @return 영어 여부
     */
    public boolean isEnglish() {
        return "en".equals(this.langCode);
    }

    /**
     * 일본어 콘텐츠인지 확인
     * @return 일본어 여부
     */
    public boolean isJapanese() {
        return "ja".equals(this.langCode);
    }

    /**
     * 중국어 콘텐츠인지 확인
     * @return 중국어 여부
     */
    public boolean isChinese() {
        return "zh".equals(this.langCode);
    }
}
