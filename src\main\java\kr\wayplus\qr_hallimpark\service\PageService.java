package kr.wayplus.qr_hallimpark.service;

import java.util.ArrayList;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import kr.wayplus.qr_hallimpark.mapper.PageMapper;
import kr.wayplus.qr_hallimpark.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PageService {
    private final PageMapper pageMapper;
    private final UserMapper userMapper;
    
    public PageService(PageMapper pageMapper, UserMapper userMapper) {
        this.pageMapper = pageMapper;
        this.userMapper = userMapper;
    }
	
}
