/* 관리자 인증 레이아웃 전용 스타일 */

/* 전체 페이지 설정 */


/* 메인 콘텐츠 영역 */
.auth-main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
}

/* 배경 패턴 */
.auth-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    background-size: 100px 100px;
    pointer-events: none;
}

/* 관리자 로그인 컨테이너 */
.admin-login-container {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 500px;
}

/* 관리자 로그인 카드 */
.admin-login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
}

/* 슬라이드 업 애니메이션 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 관리자 로그인 헤더 */
.admin-login-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    text-align: center;
    padding: 3rem 2rem 2rem;
    position: relative;
}

.admin-login-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 80%);
}

.admin-login-header i {
    color: #3498db;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.admin-login-header h2 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 관리자 로그인 바디 */
.admin-login-body {
    padding: 2rem;
}

/* 관리자 배지 */
.admin-badge {
    display: inline-block;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

/* 보안 안내 */
.security-notice {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    font-size: 0.875rem;
    color: #856404;
}

/* 폼 스타일 */
.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-floating .form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-floating label {
    color: #6c757d;
    font-weight: 500;
}

/* 체크박스 스타일 */
.remember-me {
    margin-bottom: 2rem;
}

.form-check-input:checked {
    background-color: #3498db;
    border-color: #3498db;
}

/* 로그인 버튼 */
.btn-admin-login {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-admin-login:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-admin-login:active {
    transform: translateY(0);
}

/* 관리자 로그인 푸터 */
.admin-login-footer {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
}

.admin-login-footer a {
    color: #6c757d;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.admin-login-footer a:hover {
    color: #3498db;
}

/* 반응형 디자인 */
@media (max-width: 576px) {
    .auth-main {
        padding: 1rem 0.5rem;
    }
    
    .admin-login-header {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .admin-login-header i {
        font-size: 3rem;
    }
    
    .admin-login-body {
        padding: 1.5rem;
    }
    
    .admin-login-footer {
        padding: 1rem 1.5rem;
    }
}

/* 로딩 상태 */
.btn-admin-login:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 에러 상태 */
.form-control.is-invalid {
    border-color: #e74c3c;
}

.invalid-feedback {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
