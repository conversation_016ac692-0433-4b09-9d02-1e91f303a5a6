<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/error}">
<head>
    <title>서버 오류 - 한림공원 QR 체험</title>
    <meta name="description" content="서버에서 오류가 발생했습니다.">
</head>
<body>
    <div layout:fragment="content">
        <!-- 에러 아이콘 -->
        <i class="fas fa-server text-danger error-icon"></i>

        <!-- 에러 코드 -->
        <div class="error-code text-danger">500</div>

        <!-- 에러 메시지 -->
        <h1 class="error-title">앗! 서버에 문제가 생겼어요</h1>
        <p class="error-description">
            죄송합니다. 일시적인 서버 문제로 인해<br>
            요청하신 작업을 완료할 수 없습니다.<br><br>
            <strong>잠시 후 다시 시도해주시거나, 문제가 지속되면 관리자에게 문의해주세요.</strong>
        </p>

        <!-- 도움말 정보 -->
        <div class="error-help">
            <h5>이런 경우에 도움이 될 수 있어요:</h5>
            <ul class="text-start">
                <li>페이지를 새로고침해보세요</li>
                <li>잠시 후 다시 시도해보세요</li>
                <li>브라우저 캐시를 삭제해보세요</li>
                <li>다른 브라우저로 접속해보세요</li>
            </ul>
        </div>

        <!-- 액션 버튼들 -->
        <div class="error-actions">
            <button type="button" class="btn btn-primary" data-action="reload">
                <i class="fas fa-redo-alt me-2"></i>다시 시도하기
            </button>
            <button type="button" class="btn btn-outline-secondary" data-action="back">
                <i class="fas fa-arrow-left me-2"></i>뒤로가기
            </button>
            <a th:href="@{/}" class="btn btn-outline-primary">
                <i class="fas fa-home me-2"></i>홈으로 돌아가기
            </a>
        </div>

        <!-- 자동 새로고침 카운터 -->
        <div class="auto-refresh mt-4">
            <small class="text-muted">
                <span id="autoRefreshCounter">30</span>초 후 자동으로 새로고침됩니다.
                <button type="button" class="btn btn-link btn-sm p-0 ms-2" id="cancelAutoRefresh">
                    취소
                </button>
            </small>
        </div>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="scripts">
        <script>
            $(document).ready(function() {
                let autoRefreshTimer;
                let countdown = 30;
                let isAutoRefreshCanceled = false;

                // 자동 새로고침 카운터
                function startAutoRefresh() {
                    autoRefreshTimer = setInterval(function() {
                        countdown--;
                        $('#autoRefreshCounter').text(countdown);

                        if (countdown <= 0 && !isAutoRefreshCanceled) {
                            window.location.reload();
                        }
                    }, 1000);
                }

                // 자동 새로고침 취소
                $('#cancelAutoRefresh').on('click', function() {
                    isAutoRefreshCanceled = true;
                    clearInterval(autoRefreshTimer);
                    $('.auto-refresh').hide();
                });

                // 버튼 이벤트 처리
                $('[data-action="reload"]').on('click', function() {
                    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>새로고침 중...');
                    window.location.reload();
                });

                $('[data-action="back"]').on('click', function() {
                    if (window.history.length > 1) {
                        window.history.back();
                    } else {
                        window.location.href = '/';
                    }
                });

                // 자동 새로고침 시작
                startAutoRefresh();
            });
        </script>
    </th:block>
</body>
</html>
