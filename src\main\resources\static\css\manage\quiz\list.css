/**
 * 문제 목록 페이지 스타일
 */

/* 다국어 버튼 컨테이너 */
.language-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    justify-content: flex-start;
    align-items: center;
}

/* 다국어 버튼 기본 스타일 */
.language-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 45px;
    text-align: center;
    line-height: 1.2;
}

/* 활성화된 다국어 버튼 (파란 배경, 하얀 글자) */
.language-btn.active {
    background-color: #4285f4;
    color: #ffffff;
}

.language-btn.active:hover {
    background-color: #3367d6;
}

/* 비활성화된 다국어 버튼 (회색 배경, 검은 글자) */
.language-btn.inactive {
    background-color: #e8e8e8;
    color: #333333;
}

.language-btn.inactive:hover {
    background-color: #d4d4d4;
}

/* 한국어 버튼 (항상 활성화, 클릭 불가 표시) */
.language-btn[data-lang="ko"].active {
    cursor: default;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .language-buttons {
        gap: 2px;
    }
    
    .language-btn {
        padding: 3px 6px;
        font-size: 11px;
        min-width: 40px;
    }
}

/* 테이블 셀 내 다국어 버튼 정렬 */
td .language-buttons {
    justify-content: center;
}
