문제 목록 조회: GET Q알작지/api/quizzes?page=1&size=20&categoryId=1
QR-문제 연결: POST WayQRAPI서버/api/assignments
연결 조회: GET Q알작지/api/assignments/qr/{qrCodeId}
연결 삭제: DELETE Q알작지/api/assignments/{mappingId}

### **문제 - QR 연결 흐름**

**목표**: **`Vue3`** 화면에서 **`Thymeleaf`** 시스템에 있는 문제를 선택하여 **`API`** 서버의 QR과 연결한다.

**관리자 '김팀장'의 시점:**

1.  김팀장이 QR 관리 페이지에 접속하면, 브라우저는 **`Vue3`** 애플리케이션을 실행합니다.
    *   *(내부적으로 `Vue3`는 `API` 서버의 `GET /api/qrcodes`를 호출하여 QR 목록을 가져옵니다.)*

2.  김팀장이 `Vue3` 화면의 특정 QR 옆에 있는 **[문제 연결]** 버튼을 클릭합니다.

3.  `Vue3`는 **'문제 선택 모달'** 컴포넌트를 화면에 띄웁니다.

4.  **[핵심 요청 1: Vue3 → Thymeleaf]**
    *   모달이 열리는 순간, **`Vue3`**의 JavaScript 코드는 **`Thymeleaf`** 서버의 `GET /api/quizzes` 엔드포인트로 **직접 API를 요청**합니다.
    *   **요청 목적**: `Thymeleaf` 서버가 관리하는 문제 목록 데이터를 JSON 형식으로 받기 위함입니다.
    *   **필수 조건**: `Thymeleaf` 서버는 `Vue3`가 실행되는 도메인からの 요청을 허용하도록 **CORS 설정**이 되어 있어야 합니다.

5.  **`Thymeleaf`** 서버는 요청을 받고, 자신의 DB에서 문제 목록을 조회하여 **JSON 데이터로 응답**합니다.

6.  **`Vue3`**는 응답받은 JSON 데이터를 이용해 모달 창에 문제 목록을 렌더링합니다.

7.  김팀장은 모달 창에서 원하는 문제(예: `quizId: 101`)를 **[선택]**합니다.

8.  **[핵심 요청 2: Vue3 → API]**
    *   [선택] 버튼이 클릭되면, **`Vue3`**의 JavaScript 코드는 이번에는 **`API`** 서버의 `POST /api/qrcodes/{qrCodeId}/link-quiz`와 같은 새로운 엔드포인트로 **API를 요청**합니다.
    *   **요청 목적**: 현재 작업 중인 QR에 방금 선택한 문제를 연결해달라고 요청하기 위함입니다. (요청 Body에는 `{ "quizId": 101 }`이 포함됩니다.)

9.  **[핵심 요청 3: API → Thymeleaf (서버 간 통신)]**
    *   **`API`** 서버는 이 요청을 받고, 최종 연결 정보를 저장하기 위해 **`Thymeleaf`** 서버의 `POST /api/assignments` 엔드포인트로 **서버 간(Server-to-Server) 통신**을 시작합니다.
    *   `API` 서버의 Java 코드(예: `RestTemplate` 사용)가 `Thymeleaf` 서버의 API를 호출하는 것입니다.
    *   **요청 목적**: "QR-문제 연결 정보를 생성해달라"고 `Thymeleaf` 서버에 최종적으로 위임하는 것입니다.
    *   **필수 조건**: `Thymeleaf` 서버는 이 API에 대해 **IP 화이트리스트나 사전 공유된 API 키(Secret Key) 등 서버 간 인증 메커니즘**을 적용하여, 오직 신뢰할 수 있는 **`API`** 서버만이 호출할 수 있도록 보안을 강화해야 합니다.

10. **`Thymeleaf`** 서버는 요청을 받고 자신의 DB에 연결 정보를 저장한 후, 성공 여부를 **`API`** 서버에 응답합니다.

11. **`API`** 서버는 `Thymeleaf` 서버로부터 성공 응답을 받으면, **`Vue3`**에게 최종 성공 응답을 보냅니다.

12. **`Vue3`**는 최종 성공 응답을 받고, 모달 창을 닫고 사용자에게 "연결 완료" 알림을 띄워줍니다.

### **요약: 요청의 흐름**

**`Vue3` (브라우저)** ➡️ **`Thymeleaf` (문제 목록 조회)**

**`Vue3` (브라우저)** ➡️ **`API` (연결 요청 시작)** ➡️ **`Thymeleaf` (최종 저장)** ➡️ **`API` (결과 수신)** ➡️ **`Vue3` (최종 결과 확인)**