<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/error}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>접근 권한 없음 - 한림공원 QR 체험</title>
    <meta name="description" content="해당 페이지에 접근할 권한이 없습니다.">
</head>
<body>
    <div layout:fragment="content">
        <!-- 에러 아이콘 -->
        <i class="fas fa-shield-alt text-warning error-icon"></i>

        <!-- 에러 코드 -->
        <div class="error-code text-warning">403</div>

        <!-- 에러 메시지 -->
        <h1 class="error-title">앗! 접근할 수 없는 영역이에요</h1>
        <p class="error-description">
            죄송합니다. 해당 페이지에 접근할 권한이 없습니다.<br>
            <strong>로그인이 필요하거나 특별한 권한이 필요할 수 있어요.</strong>
        </p>

        <!-- 로그인 상태에 따른 다른 메시지 -->
        <div sec:authorize="!isAuthenticated()" class="alert alert-info border-0 mb-4">
            <h6 class="alert-heading">
                <i class="fas fa-user-circle me-2"></i>로그인이 필요해요
            </h6>
            <p class="mb-0">
                이 페이지를 보시려면 먼저 로그인해주세요.<br>
                로그인 후 다시 시도해보세요!
            </p>
        </div>

        <div sec:authorize="isAuthenticated()" class="alert alert-warning border-0 mb-4">
            <h6 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>권한이 부족해요
            </h6>
            <p class="mb-0">
                현재 계정으로는 이 페이지에 접근할 수 없습니다.<br>
                관리자 권한이 필요하거나 접근이 제한된 페이지일 수 있어요.
            </p>
        </div>

        <!-- 도움말 정보 -->
        <div class="error-help">
            <h5>이런 방법들을 시도해보세요:</h5>
            <ul class="text-start">
                <li sec:authorize="!isAuthenticated()">로그인 후 다시 접속해보세요</li>
                <li sec:authorize="isAuthenticated()">다른 계정으로 로그인해보세요</li>
                <li>관리자에게 권한 요청을 문의해보세요</li>
                <li>홈페이지에서 다른 페이지를 이용해보세요</li>
                <li>뒤로가기 버튼으로 이전 페이지로 돌아가세요</li>
            </ul>
        </div>

        <!-- 액션 버튼들 -->
        <div class="error-actions">
            <!-- 비로그인 사용자용 버튼 -->
            <div sec:authorize="!isAuthenticated()">
                <a th:href="@{/user/login}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>로그인하기
                </a>
            </div>
            <button type="button" class="btn btn-outline-secondary" data-action="back">
                <i class="fas fa-arrow-left me-2"></i>뒤로가기
            </button>
            <a th:href="@{/}" class="btn btn-outline-primary">
                <i class="fas fa-home me-2"></i>홈으로 돌아가기
            </a>
        </div>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="scripts">
        <script>
            $(document).ready(function() {
                // 뒤로가기 버튼 처리
                $('[data-action="back"]').on('click', function() {
                    if (window.history.length > 1) {
                        window.history.back();
                    } else {
                        window.location.href = '/';
                    }
                });
            });
        </script>
    </th:block>
</body>
</html>
