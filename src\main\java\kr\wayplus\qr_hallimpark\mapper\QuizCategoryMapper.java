package kr.wayplus.qr_hallimpark.mapper;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.model.QuizCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 문제 카테고리 매퍼
 * - quiz_category 테이블 CRUD 처리
 * - 공통 리스트 기능 지원
 */
@Mapper
@Repository
public interface QuizCategoryMapper extends AdminListMapper<QuizCategory> {
    
    /**
     * 모든 문제 카테고리 목록 조회
     * @return 문제 카테고리 목록
     */
    List<QuizCategory> selectQuizCategoryList();
    
    /**
     * 카테고리 ID로 문제 카테고리 조회
     * @param categoryId 카테고리 ID
     * @return 문제 카테고리 정보
     */
    QuizCategory selectQuizCategoryById(Long categoryId);
    
    /**
     * 카테고리명으로 문제 카테고리 조회
     * @param categoryName 카테고리명
     * @return 문제 카테고리 정보
     */
    QuizCategory selectQuizCategoryByName(String categoryName);
    
    /**
     * 문제 카테고리 등록
     * @param quizCategory 문제 카테고리 정보
     * @return 등록된 행 수
     */
    int insertQuizCategory(QuizCategory quizCategory);
    
    /**
     * 문제 카테고리 수정
     * @param quizCategory 문제 카테고리 정보
     * @return 수정된 행 수
     */
    int updateQuizCategory(QuizCategory quizCategory);
    
    /**
     * 문제 카테고리 삭제 (소프트 삭제)
     * @param quizCategory 삭제할 카테고리 정보 (categoryId, deleteId 포함)
     * @return 삭제된 행 수
     */
    int deleteQuizCategory(QuizCategory quizCategory);
    
    /**
     * 카테고리명 중복 체크
     * @param categoryName 카테고리명
     * @return 중복 개수
     */
    int countByCategoryName(String categoryName);
    
    /**
     * 카테고리명 중복 체크 (수정 시 자기 자신 제외)
     * @param categoryName 카테고리명
     * @param categoryId 제외할 카테고리 ID
     * @return 중복 개수
     */
    int countByCategoryNameExcludeId(@Param("categoryName") String categoryName, @Param("categoryId") Long categoryId);

    // ========== 계층형 구조 관련 메서드 ==========

    /**
     * 최상위 카테고리 목록 조회 (parent_id가 NULL인 카테고리들)
     * @return 최상위 카테고리 목록
     */
    List<QuizCategory> selectRootCategories();

    /**
     * 특정 부모 카테고리의 자식 카테고리 목록 조회
     * @param parentId 부모 카테고리 ID
     * @return 자식 카테고리 목록
     */
    List<QuizCategory> selectChildCategories(Long parentId);

    /**
     * 계층형 구조로 모든 카테고리 조회 (부모 카테고리명 포함)
     * @return 계층형 카테고리 목록
     */
    List<QuizCategory> selectCategoriesWithHierarchy();

    /**
     * 특정 카테고리의 모든 하위 카테고리 ID 목록 조회 (재귀적)
     * @param categoryId 상위 카테고리 ID
     * @return 하위 카테고리 ID 목록
     */
    List<Long> selectDescendantCategoryIds(Long categoryId);

    /**
     * 특정 카테고리의 상위 카테고리 경로 조회
     * @param categoryId 카테고리 ID
     * @return 상위 카테고리 경로 목록 (최상위부터 현재까지)
     */
    List<QuizCategory> selectCategoryPath(Long categoryId);

    /**
     * 부모 카테고리 ID로 자식 카테고리 개수 조회
     * @param parentId 부모 카테고리 ID (NULL이면 최상위 카테고리 개수)
     * @return 자식 카테고리 개수
     */
    int countChildCategories(Long parentId);

    /**
     * 카테고리 ID로 깊이(depth) 조회
     * @param categoryId 카테고리 ID
     * @return 카테고리 깊이 (최상위: 0, 1단계 서브: 1...)
     */
    Integer selectCategoryDepth(Long categoryId);

    /**
     * 순환 참조 체크를 위한 상위 카테고리 ID 목록 조회
     * @param categoryId 카테고리 ID
     * @return 상위 카테고리 ID 목록
     */
    List<Long> selectAncestorCategoryIds(Long categoryId);

    /**
     * 부모 카테고리 선택용 목록 조회 (특정 카테고리와 그 하위 카테고리 제외)
     * @param excludeCategoryId 제외할 카테고리 ID (NULL이면 모든 카테고리 조회)
     * @return 부모 카테고리 선택 가능한 목록
     */
    List<QuizCategory> selectParentCategoryOptions(Long excludeCategoryId);
}
