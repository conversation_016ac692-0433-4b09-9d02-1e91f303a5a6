<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/error}">
<head>
    <title>페이지를 찾을 수 없습니다 - 한림공원 QR 체험</title>
    <meta name="description" content="요청하신 페이지를 찾을 수 없습니다.">
</head>
<body>
    <div layout:fragment="content">
        <!-- 에러 아이콘 -->
        <i class="fas fa-map-marker-alt text-primary error-icon"></i>

        <!-- 에러 코드 -->
        <div class="error-code text-primary">404</div>

        <!-- 에러 메시지 -->
        <h1 class="error-title">길을 잃으셨나요?</h1>
        <p class="error-description">
            요청하신 페이지를 찾을 수 없습니다.<br>
            페이지가 이동되었거나 삭제되었을 수 있어요.<br><br>
            <strong>아래 방법들을 시도해보세요!</strong>
        </p>

        <!-- 도움말 정보 -->
        <div class="error-help">
            <h5>이런 방법들을 시도해보세요:</h5>
            <ul class="text-start">
                <li>URL 주소를 다시 확인해보세요</li>
                <li>홈페이지에서 원하는 페이지를 찾아보세요</li>
                <li>뒤로가기 버튼을 눌러 이전 페이지로 돌아가세요</li>
                <li>검색 기능을 이용해보세요</li>
            </ul>
        </div>

        <!-- 액션 버튼들 -->
        <div class="error-actions">
            <a th:href="@{/}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>홈으로 돌아가기
            </a>
            <button type="button" class="btn btn-outline-secondary" data-action="back">
                <i class="fas fa-arrow-left me-2"></i>뒤로가기
            </button>
        </div>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="scripts">
        <script>
            $(document).ready(function() {
                // 뒤로가기 버튼 처리
                $('[data-action="back"]').on('click', function() {
                    if (window.history.length > 1) {
                        window.history.back();
                    } else {
                        window.location.href = '/';
                    }
                });
            });
        </script>
    </th:block>
</body>
</html>
