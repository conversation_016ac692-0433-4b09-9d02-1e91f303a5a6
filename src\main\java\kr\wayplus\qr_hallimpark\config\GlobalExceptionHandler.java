package kr.wayplus.qr_hallimpark.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * 전역 예외 처리기
 * - Thymeleaf 뷰 요청: ModelAndView로 HTML 에러 페이지 반환
 * - API 요청: ResponseEntity로 JSON 에러 응답 반환
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 404 에러 처리 (페이지를 찾을 수 없음)
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public Object handleNotFound(NoHandlerFoundException ex, HttpServletRequest request, HttpServletResponse response) {
        logger.warn("404 Error - Page not found: {}", request.getRequestURI());
        
        if (isApiRequest(request)) {
            return createApiErrorResponse(HttpStatus.NOT_FOUND, "페이지를 찾을 수 없습니다.", ex.getMessage());
        }
        
        ModelAndView modelAndView = new ModelAndView("error/404");
        modelAndView.addObject("errorCode", "404");
        modelAndView.addObject("errorMessage", "요청하신 페이지를 찾을 수 없습니다.");
        modelAndView.addObject("requestUrl", request.getRequestURI());
        response.setStatus(HttpStatus.NOT_FOUND.value());
        return modelAndView;
    }

    /**
     * 403 에러 처리 (접근 권한 없음)
     */
    @ExceptionHandler(org.springframework.security.access.AccessDeniedException.class)
    public Object handleAccessDenied(org.springframework.security.access.AccessDeniedException ex, HttpServletRequest request, HttpServletResponse response) {
        logger.warn("403 Error - Access denied: {}", request.getRequestURI());
        
        if (isApiRequest(request)) {
            return createApiErrorResponse(HttpStatus.FORBIDDEN, "접근 권한이 없습니다.", ex.getMessage());
        }
        
        ModelAndView modelAndView = new ModelAndView("error/403");
        modelAndView.addObject("errorCode", "403");
        modelAndView.addObject("errorMessage", "해당 페이지에 접근할 권한이 없습니다.");
        modelAndView.addObject("requestUrl", request.getRequestURI());
        response.setStatus(HttpStatus.FORBIDDEN.value());
        return modelAndView;
    }

    /**
     * 500 에러 처리 (서버 내부 오류)
     */
    @ExceptionHandler(Exception.class)
    public Object handleInternalServerError(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        logger.error("500 Error - Internal server error: {}", request.getRequestURI(), ex);
        
        if (isApiRequest(request)) {
            return createApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "서버 내부 오류가 발생했습니다.", ex.getMessage());
        }
        
        ModelAndView modelAndView = new ModelAndView("error/500");
        modelAndView.addObject("errorCode", "500");
        modelAndView.addObject("errorMessage", "서버에서 오류가 발생했습니다. 잠시 후 다시 시도해주세요.");
        modelAndView.addObject("requestUrl", request.getRequestURI());
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return modelAndView;
    }

    /**
     * 일반적인 런타임 예외 처리
     */
    @ExceptionHandler(RuntimeException.class)
    public Object handleRuntimeException(RuntimeException ex, HttpServletRequest request, HttpServletResponse response) {
        logger.error("Runtime Exception: {}", request.getRequestURI(), ex);
        
        if (isApiRequest(request)) {
            return createApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "처리 중 오류가 발생했습니다.", ex.getMessage());
        }
        
        ModelAndView modelAndView = new ModelAndView("error/error");
        modelAndView.addObject("errorCode", "ERROR");
        modelAndView.addObject("errorMessage", "요청 처리 중 오류가 발생했습니다.");
        modelAndView.addObject("requestUrl", request.getRequestURI());
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return modelAndView;
    }

    /**
     * AJAX 요청인지 확인
     * - Content-Type이 application/json인 경우
     * - Accept 헤더에 application/json이 포함된 경우
     * - X-Requested-With 헤더가 XMLHttpRequest인 경우 (AJAX)
     */
    private boolean isApiRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        String acceptHeader = request.getHeader("Accept");
        String xRequestedWith = request.getHeader("X-Requested-With");

        return (contentType != null && contentType.contains("application/json")) ||
               (acceptHeader != null && acceptHeader.contains("application/json")) ||
               "XMLHttpRequest".equals(xRequestedWith);
    }

    /**
     * API 에러 응답 생성
     */
    @ResponseBody
    private ResponseEntity<Map<String, Object>> createApiErrorResponse(HttpStatus status, String message, String detail) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", status.value());
        errorResponse.put("errorMessage", message);
        errorResponse.put("detail", detail);
        errorResponse.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(status).body(errorResponse);
    }
}
