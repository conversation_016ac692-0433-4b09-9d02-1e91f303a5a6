<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- 페이지별 타이틀 -->
    <title th:text="${pageTitle != null ? pageTitle + ' - 한림공원 관리시스템' : '한림공원 관리시스템'}">한림공원 관리시스템</title>
    
    <!-- 메타 태그 -->
    <meta name="description" th:content="${pageDescription != null ? pageDescription : '한림공원 QR 체험 관리자 시스템'}">
    <meta name="keywords" content="한림공원, 관리시스템, 관리자, QR코드">
    <meta name="author" content="WayPlus">
    
    <!-- 파비콘 -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/common/base.css" rel="stylesheet">
    <link href="/css/manage/common.css" rel="stylesheet">
    <link href="/css/manage/sidenav.css" rel="stylesheet">
    
    <!-- 페이지별 CSS -->
    <th:block th:if="${pageCss != null}">
        <link th:each="css : ${pageCss}" th:href="@{'/css/manage/' + ${css} + '.css'}" rel="stylesheet">
    </th:block>
    
    <!-- 추가 헤드 콘텐츠 -->
    <th:block layout:fragment="head"></th:block>
</head>
<body th:class="${bodyClass != null ? bodyClass : ''}" class="manage-layout">
    <!-- 사이드 네비게이션 -->
    <nav th:replace="~{fragments/manage-sidenav :: sidenav}"></nav>

    <!-- 메인 콘텐츠 영역 -->
    <div class="main-wrapper">
        <!-- 상단 헤더 (간소화된 버전) -->
        <header class="manage-header">
            <div class="header-content">
                <button class="sidenav-toggle-btn d-lg-none" id="sidenavToggleBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="header-title">
                    <h1 th:text="${pageTitle != null ? pageTitle : '관리자 페이지'}">관리자 페이지</h1>
                </div>
                <div class="header-actions">
                    <div class="user-info d-none d-md-flex align-items-center">
                        <span class="me-3" sec:authentication="name">관리자</span>
                        <form th:action="@{/manage/logout}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                로그아웃
                            </button>
                        </form>
                    </div>
                    <!-- 모바일용 로그아웃 버튼 -->
                    <div class="d-md-none">
                        <form th:action="@{/manage/logout}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </header>

        <!-- 메인 콘텐츠 -->
        <main class="main-content" role="main">
            <!-- 페이지 콘텐츠 -->
            <th:block layout:fragment="content"></th:block>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- CSRF 토큰 전역 설정 -->
    <script th:inline="javascript">
        window.csrfToken = {
            headerName: /*[[${_csrf.headerName}]]*/ 'X-CSRF-TOKEN',
            token: /*[[${_csrf.token}]]*/ 'csrf-token-value'
        };
    </script>

    <script src="/js/manage/common.js"></script>
    <script src="/js/manage/sidenav.js"></script>
    
    <!-- 페이지별 JavaScript -->
    <th:block th:if="${pageJs != null}">
        <script th:each="js : ${pageJs}" th:src="@{'/js/manage/' + ${js} + '.js'}"></script>
    </th:block>
    
    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script"></th:block>
    <th:block layout:fragment="scripts"></th:block>
</body>
</html>
