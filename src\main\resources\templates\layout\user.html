<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- 페이지별 타이틀 -->
    <title th:text="${pageTitle != null ? pageTitle + ' - 한림공원 QR 체험' : '한림공원 QR 체험'}">한림공원 QR 체험</title>
    
    <!-- 메타 태그 -->
    <meta name="description" th:content="${pageDescription != null ? pageDescription : '한림공원에서 QR 코드를 스캔하여 다양한 퀴즈와 게임을 즐겨보세요.'}">
    <meta name="keywords" content="한림공원, QR코드, 퀴즈, 게임, 체험">
    <meta name="author" content="WayPlus">
    
    <!-- Open Graph 메타 태그 (SNS 공유용) -->
    <meta property="og:title" th:content="${pageTitle != null ? pageTitle + ' - 한림공원 QR 체험' : '한림공원 QR 체험'}">
    <meta property="og:description" th:content="${pageDescription != null ? pageDescription : '한림공원에서 QR 코드를 스캔하여 다양한 퀴즈와 게임을 즐겨보세요.'}">
    <meta property="og:type" content="website">
    <meta property="og:url" th:content="${currentUrl != null ? currentUrl : 'http://localhost:8080/'}">
    <meta property="og:image" content="/images/og-image.jpg">
    
    <!-- 파비콘 -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/common/base.css" rel="stylesheet">
    <link href="/css/user/common.css" rel="stylesheet">
    
    <!-- 페이지별 CSS -->
    <th:block th:if="${pageCss != null}">
        <link th:each="css : ${pageCss}" th:href="@{'/css/user/' + ${css} + '.css'}" rel="stylesheet">
    </th:block>
    
    <!-- 추가 헤드 콘텐츠 -->
    <th:block layout:fragment="head"></th:block>
</head>
<body th:class="${bodyClass != null ? bodyClass : ''}">
    <!-- 헤더 -->
    <header th:replace="~{fragments/user-header :: header}"></header>
    
    <!-- 메인 콘텐츠 -->
    <main class="main-content" role="main">
        <!-- 페이지 콘텐츠 -->
        <th:block layout:fragment="content"></th:block>
    </main>
    
    <!-- 푸터 -->
    <footer th:replace="~{fragments/user-footer :: footer}"></footer>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="/js/user/common.js"></script>
    
    <!-- 페이지별 JavaScript -->
    <th:block th:if="${pageJs != null}">
        <script th:each="js : ${pageJs}" th:src="@{'/js/user/' + ${js} + '.js'}"></script>
    </th:block>
    
    <!-- 추가 스크립트 -->
    <th:block layout:fragment="scripts"></th:block>
</body>
</html>
