package kr.wayplus.qr_hallimpark.mapper;

import kr.wayplus.qr_hallimpark.model.LoginAttemptLog;
import kr.wayplus.qr_hallimpark.model.LoginUser;
import kr.wayplus.qr_hallimpark.model.LoginUserSession;
import kr.wayplus.qr_hallimpark.model.UserWebLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.HashMap;

@Mapper
@Repository
public interface UserMapper {
    LoginUser selectUserByUserid(String username);

    void updateUserSessionLogout(LoginUserSession loginUserSession);

    void insertUserLoginAttemptLog(LoginAttemptLog attemptLog);

    void updateUserWebLog(HashMap<String, String> param);

    void updateUserNewTokenId(LoginUser user);

    void insertUserWebLog(UserWebLog webLog);

    void insertUserLoginLog(HashMap<String, String> parameterMap);

    void updateUserLastLoginDate(LoginUser user);

    int selectUserCountById(String id);

    void insertNewUser(LoginUser user);

    void updateUser(LoginUser user);
    
    LoginUser selectUserIdByUserInfo(LoginUser user);

    LoginUser selectUserRePasswordByUserInfo(LoginUser user);

    LoginUser selectUserByUserToken(LoginUser user);

    void updateUserPassword(LoginUser storedUser);
}
