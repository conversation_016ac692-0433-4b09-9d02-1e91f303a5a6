<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>문제 관리 - 한림공원 관리시스템</title>
    <meta name="description" content="문제를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/common/admin-list.css" rel="stylesheet">
    <link href="/css/manage/content/quiz.css" rel="stylesheet">
    <link href="/css/manage/quiz/list.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="quiz-page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>문제 관리</h2>
                        <p>문제를 관리합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/quiz/new}" class="btn btn-primary">
                            새 문제 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 성공 메시지 -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <span th:text="${successMessage}">성공 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 최상위 카테고리 필터 -->
                <div class="category-filter-section">
                    <div class="row">
                        <div class="col-12">
                            <div class="category-filter-container">
                                <h5 class="filter-title">카테고리 선택</h5>
                                <div class="category-buttons">
                                    <button type="button" class="btn btn-outline-primary category-btn active" data-category-id="all">
                                        전체
                                    </button>
                                    <button type="button" 
                                            th:each="category : ${rootCategories}"
                                            th:data-category-id="${category.categoryId}"
                                            th:text="${category.categoryName}"
                                            class="btn btn-outline-primary category-btn">
                                        카테고리명
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 문제 목록 -->
                <!-- 공통 검색/필터/정렬 컨트롤 -->
                <div th:replace="~{fragments/admin-list-controls :: controls(
                    searchFields='title:문제명,category_name:카테고리명,quiz_type:게임유형',
                    filterOptions='',
                    sortOptions='quiz_id:등록순,title:문제명,category_name:카테고리명,create_date:등록일,last_update_date:수정일',
                    searchPlaceholder='검색어를 입력해주세요.',
                    showDateRange=false,
                    showNumberRange=false
                )}"></div>

                <!-- 동적 테이블 컨테이너 -->
                <div th:replace="~{fragments/admin-list-controls :: table-container}"></div>

                <!-- 페이징 네비게이션 -->
                <div th:replace="~{fragments/admin-list-controls :: pagination(${listResponse})}"></div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script src="/js/manage/admin-list-manager.js"></script>
        <script th:inline="javascript">
            // 페이지별 설정
            const pageConfig = {
                apiEndpoint: '/manage/quiz/search',
                customRowRenderer: function(items, response) {
                    if (!items || items.length === 0) {
                        return `
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">등록된 문제가 없습니다</h5>
                                <p class="text-muted">새로운 문제를 등록해보세요.</p>
                                <a href="/manage/quiz/new" class="btn btn-primary">
                                    첫 번째 문제 등록하기
                                </a>
                            </div>
                        `;
                    }

                    let html = `
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 60px;">번호</th>
                                        <th style="width: 200px;">문제명</th>
                                        <th style="width: 80px;">상태</th>
                                        <th style="width: 120px;">카테고리</th>
                                        <th style="width: 120px;">서브카테고리</th>
                                        <th style="width: 120px;">게임유형</th>
                                        <th style="width: 100px;">다국어설정</th>
                                        <th style="width: 80px;">QR연동</th>
                                        <th style="width: 100px;">등록일</th>
                                        <th style="width: 100px;">수정일</th>
                                        <th style="width: 150px;">관리</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    items.forEach((item, index) => {
                        const rowNumber = (response.currentPage - 1) * response.pageSize + index + 1;
                        const createDate = item.createDate ? `${new Date(item.createDate).toISOString().split('T')[0]}` : '-';
                        const updateDate = item.lastUpdateDate ? `${new Date(item.lastUpdateDate).toISOString().split('T')[0]}` : '-';
                        const isActive = item.status === 'ACTIVE';
                        const statusToggle = `
                            <div class="custom-toggle-container">
                                <button class="custom-toggle-btn ${isActive ? 'active' : 'inactive'}"
                                        data-quiz-id="${item.quizId}"
                                        data-status="${item.status}">
                                    <div class="toggle-slider">
                                        <span class="toggle-text">${isActive ? '활성' : '비활성'}</span>
                                    </div>
                                </button>
                            </div>
                        `;
                        const gameType = item.gameTypeDisplay || item.quizType || '-';
                        const parentCategory = item.parentCategoryName || '-';
                        const qrCount = item.qrMappingCount || 0;

                        // 다국어 버튼 생성
                        const languageButtons = `
                            <div class="language-buttons">
                                <button class="language-btn ${item.supportsKorean ? 'active' : 'inactive'}"
                                        data-quiz-id="${item.quizId}"
                                        data-lang="ko"
                                        data-active="${item.supportsKorean}">
                                    한국어
                                </button>
                                <button class="language-btn ${item.supportsEnglish ? 'active' : 'inactive'}"
                                        data-quiz-id="${item.quizId}"
                                        data-lang="en"
                                        data-active="${item.supportsEnglish}">
                                    영어
                                </button>
                                <button class="language-btn ${item.supportsJapanese ? 'active' : 'inactive'}"
                                        data-quiz-id="${item.quizId}"
                                        data-lang="ja"
                                        data-active="${item.supportsJapanese}">
                                    일본어
                                </button>
                                <button class="language-btn ${item.supportsChinese ? 'active' : 'inactive'}"
                                        data-quiz-id="${item.quizId}"
                                        data-lang="zh"
                                        data-active="${item.supportsChinese}">
                                    중국어
                                </button>
                            </div>
                        `;

                        html += `
                            <tr>
                                <td class="text-center">${rowNumber}</td>
                                <td>
                                    <div class="quiz-title" title="${item.title || ''}">
                                        ${item.title ? (item.title.length > 30 ? item.title.substring(0, 30) + '...' : item.title) : '-'}
                                    </div>
                                </td>
                                <td>
                                    ${statusToggle}
                                </td>
                                <td>${item.categoryName || '-'}</td>
                                <td>${parentCategory}</td>
                                <td>${gameType}</td>
                                <td>
                                    ${languageButtons}
                                </td>
                                <td class="text-center">
                                    <span class="badge ${qrCount > 0 ? 'bg-primary' : 'bg-secondary'}">
                                        ${qrCount}개
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">${createDate}</small>
                                </td>
                                <td>
                                    <small class="text-muted">${updateDate}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm edit-quiz-btn"
                                                data-quiz-id="${item.quizId}"
                                                title="문제 수정">
                                            수정
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                onclick="alert('문제 관리 기능은 향후 구현 예정입니다.');"
                                                title="문제 관리">
                                            문제관리
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    html += `
                                </tbody>
                            </table>
                        </div>
                    `;

                    return html;
                }
            };

            // 관리자 리스트 매니저 초기화
            const listManager = new AdminListManager(pageConfig);

            // 카테고리 필터 기능
            $(document).ready(function() {
                // 카테고리 버튼 클릭 이벤트
                $('.category-btn').on('click', function() {
                    const categoryId = $(this).data('category-id');
                    
                    // 버튼 상태 변경
                    $('.category-btn').removeClass('active');
                    $(this).addClass('active');
                    
                    // 카테고리별 필터링
                    if (categoryId === 'all') {
                        // 전체 조회 - 카테고리 필터 제거
                        const searchCondition = listManager.getSearchCondition();
                        if (searchCondition.filters && searchCondition.filters.categoryFilter) {
                            delete searchCondition.filters.categoryFilter;
                        }
                        searchCondition.page = 1;
                        listManager.setSearchCondition(searchCondition);
                    } else {
                        // 특정 카테고리 조회
                        filterByCategory(categoryId);
                    }
                });
            });

            // 카테고리별 필터링 함수
            function filterByCategory(categoryId) {
                const searchCondition = listManager.getSearchCondition();

                // 카테고리 필터 설정
                if (!searchCondition.filters) {
                    searchCondition.filters = {};
                }
                searchCondition.filters.categoryFilter = categoryId;
                searchCondition.page = 1; // 첫 페이지로 이동

                listManager.setSearchCondition(searchCondition);
            }

            // 상태 토글 이벤트 처리
            function bindStatusToggleEvents() {
                $(document).off('click', '.custom-toggle-btn').on('click', '.custom-toggle-btn', function() {
                    const $toggle = $(this);
                    const quizId = $toggle.data('quiz-id');
                    const $statusText = $toggle.find('.toggle-text');

                    // 토글 비활성화 (중복 클릭 방지)
                    $toggle.prop('disabled', true);

                    $.ajax({
                        url: `/manage/quiz/${quizId}/toggle-status`,
                        type: 'POST',
                        beforeSend: function(xhr) {
                            xhr.setRequestHeader(window.csrfToken.headerName, window.csrfToken.token);
                        },
                        success: function(response) {
                            if (response.success) {
                                // 상태 및 스타일 업데이트
                                const newStatus = response.newStatus;
                                const isActive = newStatus === 'ACTIVE';

                                $toggle.removeClass('active inactive').addClass(isActive ? 'active' : 'inactive');
                                $toggle.data('status', newStatus);
                                $statusText.text(response.statusDisplay);
                            } else {
                                alert(response.message || '상태 변경에 실패했습니다.');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('상태 토글 중 오류 발생:', error);
                            alert('상태 변경 중 오류가 발생했습니다.');
                        },
                        complete: function() {
                            // 토글 다시 활성화
                            $toggle.prop('disabled', false);
                        }
                    });
                });
            }

            // 수정 버튼 이벤트 처리
            function bindEditButtonEvents() {
                $(document).off('click', '.edit-quiz-btn').on('click', '.edit-quiz-btn', function() {
                    const quizId = $(this).data('quiz-id');
                    window.location.href = `/manage/quiz/${quizId}/edit`;
                });
            }

            // 다국어 버튼 이벤트 처리
            function bindLanguageButtonEvents() {
                $(document).off('click', '.language-btn').on('click', '.language-btn', function() {
                    const $btn = $(this);
                    const quizId = $btn.data('quiz-id');
                    const langCode = $btn.data('lang');
                    const isActive = $btn.data('active');

                    // 한국어는 비활성화할 수 없음
                    if (langCode === 'ko' && isActive) {
                        alert('한국어는 기본 언어로 비활성화할 수 없습니다.');
                        return;
                    }

                    // 상태 토글
                    const newStatus = !isActive;

                    // 서버에 요청
                    $.ajax({
                        url: `/manage/quiz/${quizId}/language/${langCode}`,
                        type: 'POST',
                        data: JSON.stringify({ active: newStatus }),
                        contentType: 'application/json',
                        beforeSend: function(xhr) {
                            xhr.setRequestHeader(/*[[${_csrf.headerName}]]*/ 'X-CSRF-TOKEN', /*[[${_csrf.token}]]*/ 'token');
                        },
                        success: function(response) {
                            if (response.success) {
                                // 버튼 상태 업데이트
                                $btn.removeClass('active inactive')
                                    .addClass(newStatus ? 'active' : 'inactive')
                                    .data('active', newStatus);

                                alert(response.message || '다국어 설정이 변경되었습니다.');

                                // 목록 새로고침 (약간의 지연 후 실행)
                                setTimeout(function() {
                                    if (typeof listManager !== 'undefined' && typeof listManager.loadList === 'function') {
                                        try {
                                            listManager.loadList();
                                        } catch (e) {
                                            console.warn('목록 새로고침 중 오류:', e);
                                            // 페이지 새로고침으로 대체
                                            window.location.reload();
                                        }
                                    } else {
                                        // listManager가 없으면 페이지 새로고침
                                        window.location.reload();
                                    }
                                }, 100);
                            } else {
                                alert(response.message || '다국어 설정 변경에 실패했습니다.');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('다국어 설정 변경 중 오류 발생:', error);
                            let errorMessage = '다국어 설정 변경 중 오류가 발생했습니다.';

                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }

                            alert(errorMessage);
                        }
                    });
                });
            }

            // 리스트 로드 후 이벤트 바인딩
            if (typeof listManager !== 'undefined' && listManager.loadList) {
                const originalLoadList = listManager.loadList.bind(listManager);
                listManager.loadList = function() {
                    originalLoadList();
                    // 리스트 로드 완료 후 이벤트 바인딩
                    setTimeout(function() {
                        bindStatusToggleEvents();
                        bindEditButtonEvents();
                        bindLanguageButtonEvents();
                    }, 100);
                };
            }

            // 초기 이벤트 바인딩
            bindStatusToggleEvents();
            bindEditButtonEvents();
            bindLanguageButtonEvents();
        </script>
    </th:block>
</body>
</html>
